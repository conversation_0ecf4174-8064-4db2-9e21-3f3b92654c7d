import { Dimensions, View } from "react-native"
// import Wave from 'assets/base/images/Waveform.svg'
import Svg, { Rect } from "react-native-svg";

const WaveAudio = (props: { fillColor:string }) => {    
    return (
        <View style={{
            display: 'flex',
            flexDirection: 'row'
            , alignItems: 'center',
        }}>
            <Wave fillColor={props.fillColor}/>
        </View>
    )
}
export default WaveAudio

const Wave = ({fillColor}: {fillColor:string}) => (
    <Svg width="158" height="17" viewBox="0 0 158 17" >
        <Rect x="0" y="7.5" width="2" height="2" rx="1" fill={fillColor} />
        <Rect x="6" y="4.5" width="2" height="8" rx="1" fill={fillColor} />
        <Rect x="12" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="18" y="6.5" width="2" height="4" rx="1" fill={fillColor} />
        <Rect x="24" y="0.5" width="2" height="16" rx="1" fill={fillColor} />
        <Rect x="30" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="36" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="42" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="48" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="54" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="60" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="66" y="0.5" width="2" height="16" rx="1" fill={fillColor} />
        <Rect x="72" y="7.5" width="2" height="2" rx="1" fill={fillColor} />
        <Rect x="78" y="4.5" width="2" height="8" rx="1" fill={fillColor} />
        <Rect x="84" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="90" y="6.5" width="2" height="4" rx="1" fill={fillColor} />
        <Rect x="96" y="0.5" width="2" height="16" rx="1" fill={fillColor} />
        <Rect x="102" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="108" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="114" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="120" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="126" y="1.5" width="2" height="14" rx="1" fill={fillColor} />
        <Rect x="132" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="138" y="0.5" width="2" height="16" rx="1" fill={fillColor} />
        <Rect x="144" y="3.5" width="2" height="10" rx="1" fill={fillColor} />
        <Rect x="150" y="6.5" width="2" height="4" rx="1" fill={fillColor} />
        <Rect x="156" y="7.5" width="2" height="2" rx="1" fill={fillColor} />
    </Svg>
  );