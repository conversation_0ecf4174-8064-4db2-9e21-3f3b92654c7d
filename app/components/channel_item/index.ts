// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import { withDatabase, withObservables } from '@nozbe/watermelondb/react';
import React from 'react';
import { combineLatest, of as of$ } from 'rxjs';
import { Q } from '@nozbe/watermelondb';

import { switchMap, distinctUntilChanged } from 'rxjs/operators';

import { observeChannelsWithCalls } from '@calls/state';
import { General } from '@constants';
import { withServerUrl } from '@context/server';
import { observeIsMutedSetting, observeMyChannel, queryChannelMembers } from '@queries/servers/channel';
import { queryDraft } from '@queries/servers/drafts';
import { observeCurrentChannelId, observeCurrentUserId } from '@queries/servers/system';
import { observeTeam } from '@queries/servers/team';

import ChannelItem from './channel_item';

import type { WithDatabaseArgs } from '@typings/database/database';
import type ChannelModel from '@typings/database/models/servers/channel';
import { queryPostsAt, queryPostsBetween, queryPostsInChannel } from '@app/queries/servers/post';
import { observeIsCRTEnabled } from '@app/queries/servers/thread';
import post from '@app/constants/post';

type EnhanceProps = WithDatabaseArgs & {
    channel: ChannelModel | Channel;
    showTeamName?: boolean;
    serverUrl?: string;
    shouldHighlightActive?: boolean;
    shouldHighlightState?: boolean;
}

const enhance = withObservables(['channel', 'showTeamName', 'shouldHighlightActive', 'shouldHighlightState'], ({
    channel,
    database,
    serverUrl,
    showTeamName = false,
    shouldHighlightActive = false,
    shouldHighlightState = false,
}: EnhanceProps) => {
    const currentUserId = observeCurrentUserId(database);
    const myChannel = observeMyChannel(database, channel.id);

 


    const hasDraft = shouldHighlightState ? queryDraft(database, channel.id).observeWithColumns(['message', 'files', 'metadata']).pipe(
        switchMap((drafts) => {
            if (!drafts.length) {
                return of$(false);
            }

            const draft = drafts[0];
            const standardPriority = draft?.metadata?.priority?.priority === '';

            if (!draft.message && !draft.files.length && standardPriority) {
                return of$(false);
            }

            return of$(true);
        }),
        distinctUntilChanged(),
    ) : of$(false);

    const isActive = shouldHighlightActive ?
        observeCurrentChannelId(database).pipe(
            switchMap((id) => of$(id ? id === channel.id : false)),
            distinctUntilChanged(),
        ) : of$(false);

    const isMuted = shouldHighlightState ?
        myChannel.pipe(
            switchMap((mc) => {
                if (!mc) {
                    return of$(false);
                }
                return observeIsMutedSetting(database, mc.id);
            }),
        ) : of$(false);

    const teamId = 'teamId' in channel ? channel.teamId : channel.team_id;

    const teamDisplayName = (teamId && showTeamName) ?
        observeTeam(database, teamId).pipe(
            switchMap((team) => of$(team?.displayName || '')),
            distinctUntilChanged(),
        ) : of$('');



    const membersCount = channel.type === General.GM_CHANNEL ?
        queryChannelMembers(database, channel.id).observeCount(false) :
        of$(0);



    const isUnread = shouldHighlightState ?
        myChannel.pipe(
            switchMap((mc) => of$(mc?.isUnread)),
            distinctUntilChanged(),
        ) : of$(false);


    const isUnreadPosts = shouldHighlightState ?
        myChannel.pipe(
            switchMap((mc) => of$(mc?.id)),
            distinctUntilChanged(),
        ) : of$(false);

        const mentionsCount = shouldHighlightState ?
        myChannel.pipe(
            switchMap((mc) => of$(mc?.mentionsCount)),
            distinctUntilChanged(),
        ) : of$(0);

    const lastPostDate =
        myChannel.pipe(
            switchMap((mc) => of$(mc?.lastPostAt)),
            distinctUntilChanged(),
        );


    const hasCall = observeChannelsWithCalls(serverUrl || '').pipe(
        switchMap((calls) => of$(Boolean(calls[channel.id]))),
        distinctUntilChanged(),
    );

    const isCRTEnabledObserver = observeIsCRTEnabled(database);


    let channelPost = queryPostsInChannel(database, channel?.id).observeWithColumns(['latest']);

      

    const lastPosts = combineLatest([isCRTEnabledObserver, channelPost]).pipe(
        switchMap(([isCRTEnabled, postsInChannel]) => {
            if (!postsInChannel.length) {
                return of$([]);
            }
            const { latest } = postsInChannel[0];


              

            return queryPostsAt(database, latest, channel.id).observe();
        }),
    )

    const hasFile = lastPosts ? lastPosts.pipe(
        switchMap((posts) => {
            if (!posts.length) {
                return of$(undefined);
            }
            const files = posts[0]?.files;
            return files ? files.observe() : of$(undefined);
        }),
        distinctUntilChanged(),
    ) : of$(undefined);


    return {
        channel: 'observe' in channel ? channel.observe() : of$(channel),
        currentUserId,
        hasDraft,
        isActive,
        isMuted,
        membersCount,
        isUnread,
        mentionsCount,
        teamDisplayName,
        hasCall,
        lastPostDate,
        lastPosts,
        hasFile
    };
});

export default React.memo(withDatabase(withServerUrl(enhance(ChannelItem))));

