// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/channel_list/categories/body/channel_item should match snapshot 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "opacity": 1,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "center",
          "flexDirection": "row",
        },
        false,
        false,
        false,
        {
          "minHeight": 40,
        },
      ]
    }
    testID="channel_item.hello"
  >
    <Icon
      name="globe"
      style={
        [
          [
            {
              "color": "rgba(255,255,255,0.4)",
            },
            undefined,
            undefined,
            [
              {
                "marginRight": 12,
              },
              undefined,
            ],
            {
              "fontSize": 24,
            },
          ],
          {
            "left": 1,
          },
        ]
      }
      testID="undefined.public"
    />
    <Text
      ellipsizeMode="tail"
      numberOfLines={1}
      style={
        [
          [
            {
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "color": "rgba(255,255,255,0.72)",
            },
            false,
            null,
            null,
            false,
            false,
          ],
          {
            "flex": 0,
            "flexShrink": 1,
          },
        ]
      }
      testID="channel_item.hello.display_name"
    >
      Hello!
    </Text>
    <View
      style={
        {
          "flex": 1,
        }
      }
    />
  </View>
</View>
`;

exports[`components/channel_list/categories/body/channel_item should match snapshot when it has a call 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "opacity": 1,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "center",
          "flexDirection": "row",
        },
        false,
        false,
        false,
        {
          "minHeight": 40,
        },
      ]
    }
    testID="channel_item.hello"
  >
    <Icon
      name="pencil-outline"
      style={
        [
          [
            {
              "color": "rgba(255,255,255,0.4)",
            },
            undefined,
            undefined,
            [
              {
                "marginRight": 12,
              },
              undefined,
            ],
            {
              "fontSize": 24,
            },
          ],
          {
            "left": 2,
          },
        ]
      }
      testID="undefined.draft"
    />
    <Text
      ellipsizeMode="tail"
      numberOfLines={1}
      style={
        [
          [
            {
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "color": "rgba(255,255,255,0.72)",
            },
            false,
            null,
            null,
            false,
            false,
          ],
          {
            "flex": 0,
            "flexShrink": 1,
          },
        ]
      }
      testID="channel_item.hello.display_name"
    >
      Hello!
    </Text>
    <View
      style={
        {
          "flex": 1,
        }
      }
    />
    <Icon
      name="phone-in-talk"
      size={16}
      style={
        [
          [
            {
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "color": "rgba(255,255,255,0.72)",
            },
            false,
            null,
            null,
            false,
            false,
          ],
          {
            "textAlign": "right",
          },
        ]
      }
    />
  </View>
</View>
`;

exports[`components/channel_list/categories/body/channel_item should match snapshot when it has a draft 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "opacity": 1,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "center",
          "flexDirection": "row",
        },
        false,
        false,
        false,
        {
          "minHeight": 40,
        },
      ]
    }
    testID="channel_item.hello"
  >
    <Icon
      name="pencil-outline"
      style={
        [
          [
            {
              "color": "rgba(255,255,255,0.4)",
            },
            undefined,
            undefined,
            [
              {
                "marginRight": 12,
              },
              undefined,
            ],
            {
              "fontSize": 24,
            },
          ],
          {
            "left": 2,
          },
        ]
      }
      testID="undefined.draft"
    />
    <Text
      ellipsizeMode="tail"
      numberOfLines={1}
      style={
        [
          [
            {
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "color": "rgba(255,255,255,0.72)",
            },
            false,
            null,
            null,
            false,
            false,
          ],
          {
            "flex": 0,
            "flexShrink": 1,
          },
        ]
      }
      testID="channel_item.hello.display_name"
    >
      Hello!
    </Text>
    <View
      style={
        {
          "flex": 1,
        }
      }
    />
  </View>
</View>
`;
