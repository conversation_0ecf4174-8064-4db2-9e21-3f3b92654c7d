// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

import CustomStatusEmoji from '@components/custom_status/custom_status_emoji';

import type { EmojiCommonStyle } from '@typings/components/emoji';
import { Pressable, type StyleProp } from 'react-native';
import type { UserModel } from '@app/database/models/server';
import ProfilePicture from '@app/components/profile_picture';
import { Screens } from '@app/constants';
import { goToScreen } from '@app/screens/navigation';
import { useTheme } from '@app/context/theme';

type Props = {
    channelText?:string|undefined,
    channelName?:string|undefined;
    customStatus?: UserCustomStatus;
    customStatusExpired: boolean;
    isCustomStatusEnabled: boolean;
    style: StyleProp<EmojiCommonStyle>;
    currentUser?: UserModel | undefined
}

const CustomStatus = ({
    channelText=undefined,
     channelName=undefined,
     customStatus,
      customStatusExpired,
    isCustomStatusEnabled, style,
    currentUser = undefined }: Props) => {
    const showCustomStatusEmoji = Boolean(isCustomStatusEnabled && customStatus?.emoji && !customStatusExpired);
    const theme= useTheme()
    console.log(`\n\nthis the custom status ${customStatus?.emoji}\n\n`)

    const handleUserPressable = ()=>{
        if(currentUser)
            return;
     goToScreen(
            Screens.USER_STATUS_SCREEN,
             '', {
               customStatus:customStatus!,
               channelName:channelName,
               channelText:channelText
             },
              )

        }
    


    if (!showCustomStatusEmoji) {
        return null;
    }

    return (
        <Pressable
        onPress={handleUserPressable}
        >

        <CustomStatusEmoji
            customStatus={customStatus!}
            style={style}
            />
            </Pressable>
    );
};

export default CustomStatus;
