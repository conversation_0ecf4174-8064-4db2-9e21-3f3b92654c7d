// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Test to verify that AudioFile component doesn't render strings directly
 * without proper Text wrapper components, preventing React Native rendering errors.
 */

import React from 'react';
import { render } from '@testing-library/react-native';
import AudioFile from '../audio_file';

// Mock dependencies
jest.mock('@context/theme', () => ({
    useTheme: () => ({
        buttonBg: '#007BFF',
        sidebarText: '#333',
        centerChannelBg: '#fff',
    }),
}));

jest.mock('@app/context/AudioPlayController', () => ({
    AudioPlayController: () => ({
        playAudio: jest.fn(),
        puseAudio: jest.fn(),
        seekToPosition: jest.fn(),
        getCurrentPosition: jest.fn(),
        changeCurrentSpeed: jest.fn(),
        playingState: 'none',
        currentTrack: '',
        recording: null,
        currentPosition: 0,
        fileDuration: 0,
        speedType: '1x',
    }),
}));

jest.mock('@components/profile_picture', () => {
    return function MockProfilePicture() {
        return null;
    };
});

jest.mock('../../loading', () => {
    return function MockLoading() {
        return null;
    };
});

describe('AudioFile String Rendering Safety', () => {
    const mockFile = {
        id: 'file-id',
        name: 'voice-message.m4a',
        mime_type: 'audio/m4a',
        size: 1024,
    } as any;

    const mockAuthor = {
        id: 'user-id',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
    } as any;

    const baseProps = {
        file: mockFile,
        showDate: false,
        onPress: jest.fn(),
        isFromSearch: false,
    };

    describe('Voice Message Rendering', () => {
        it('should render voice message for current user without string rendering errors', () => {
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: mockAuthor,
                isCurrentUser: true,
            };

            // This should not throw any string rendering errors
            expect(() => {
                render(<AudioFile {...props} />);
            }).not.toThrow();
        });

        it('should render voice message for other users without string rendering errors', () => {
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: mockAuthor,
                isCurrentUser: false,
            };

            // This should not throw any string rendering errors
            expect(() => {
                render(<AudioFile {...props} />);
            }).not.toThrow();
        });

        it('should render regular audio file without string rendering errors', () => {
            const props = {
                ...baseProps,
                isVoiceMessage: false,
                author: mockAuthor,
                isCurrentUser: false,
            };

            // This should not throw any string rendering errors
            expect(() => {
                render(<AudioFile {...props} />);
            }).not.toThrow();
        });

        it('should handle acknowledgment strings properly', () => {
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: mockAuthor,
                isCurrentUser: false,
                acknolowgment: 'Test acknowledgment message',
            };

            // This should not throw any string rendering errors
            expect(() => {
                render(<AudioFile {...props} />);
            }).not.toThrow();
        });

        it('should handle missing author gracefully', () => {
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: null,
                isCurrentUser: false,
            };

            // This should not throw any string rendering errors
            expect(() => {
                render(<AudioFile {...props} />);
            }).not.toThrow();
        });
    });
});
