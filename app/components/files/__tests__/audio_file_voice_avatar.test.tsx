// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Test to verify that AudioFile component shows user avatars instead of microphone icons
 * for voice messages, including for current user voice messages.
 */

describe('AudioFile Voice Message Avatar Logic', () => {
    const mockFile = {
        id: 'file-id',
        name: 'voice-message.m4a',
        mime_type: 'audio/m4a',
        size: 1024,
    } as any;

    const mockAuthor = {
        id: 'user-id',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
    } as any;

    const baseProps = {
        file: mockFile,
        showDate: false,
        onPress: jest.fn(),
        isFromSearch: false,
    };

    describe('Voice Message Behavior', () => {
        it('should show user avatar for voice messages from current user', () => {
            // When isVoiceMessage=true and author is provided, should show avatar
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: mockAuthor,
                isCurrentUser: true,
            };

            // Logic verification:
            // isVoiceMessage && author ? show ProfilePicture : show MicrophoneIcon
            const shouldShowAvatar = !!(props.isVoiceMessage && props.author);
            expect(shouldShowAvatar).toBe(true);
        });

        it('should show user avatar for voice messages from other users', () => {
            // When isVoiceMessage=true and author is provided, should show avatar
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: mockAuthor,
                isCurrentUser: false,
            };

            // Logic verification:
            // isVoiceMessage && author ? show ProfilePicture : show MicrophoneIcon
            const shouldShowAvatar = !!(props.isVoiceMessage && props.author);
            expect(shouldShowAvatar).toBe(true);
        });

        it('should show microphone icon for regular audio files', () => {
            // When isVoiceMessage=false, should show microphone icon
            const props = {
                ...baseProps,
                isVoiceMessage: false,
                author: mockAuthor,
                isCurrentUser: false,
            };

            // Logic verification:
            // isVoiceMessage && author ? show ProfilePicture : show MicrophoneIcon
            const shouldShowAvatar = !!(props.isVoiceMessage && props.author);
            expect(shouldShowAvatar).toBe(false);
        });

        it('should show microphone icon when author is not available', () => {
            // When author is not provided, should show microphone icon
            const props = {
                ...baseProps,
                isVoiceMessage: true,
                author: null,
                isCurrentUser: false,
            };

            // Logic verification:
            // isVoiceMessage && author ? show ProfilePicture : show MicrophoneIcon
            const shouldShowAvatar = !!(props.isVoiceMessage && props.author);
            expect(shouldShowAvatar).toBe(false);
        });
    });

    describe('Avatar Display Summary', () => {
        it('should correctly implement the avatar display logic', () => {
            // Test cases for different scenarios
            const testCases = [
                {
                    name: 'Current user voice message',
                    isVoiceMessage: true,
                    author: mockAuthor,
                    isCurrentUser: true,
                    expectedAvatar: true,
                    expectedIcon: false,
                },
                {
                    name: 'Other user voice message',
                    isVoiceMessage: true,
                    author: mockAuthor,
                    isCurrentUser: false,
                    expectedAvatar: true,
                    expectedIcon: false,
                },
                {
                    name: 'Regular audio file',
                    isVoiceMessage: false,
                    author: mockAuthor,
                    isCurrentUser: false,
                    expectedAvatar: false,
                    expectedIcon: true,
                },
                {
                    name: 'Voice message without author',
                    isVoiceMessage: true,
                    author: null,
                    isCurrentUser: false,
                    expectedAvatar: false,
                    expectedIcon: true,
                },
            ];

            testCases.forEach(testCase => {
                const shouldShowAvatar = !!(testCase.isVoiceMessage && testCase.author);
                const shouldShowIcon = !shouldShowAvatar;

                expect(shouldShowAvatar).toBe(testCase.expectedAvatar);
                expect(shouldShowIcon).toBe(testCase.expectedIcon);
            });
        });
    });
});
