// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Test to verify that AudioFile component properly isolates animations
 * and synchronizes dot movement with waveform animations for each instance.
 */

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AudioFile from '../audio_file';

// Mock dependencies
jest.mock('@context/theme', () => ({
    useTheme: () => ({
        buttonBg: '#007BFF',
        sidebarText: '#333',
        centerChannelBg: '#fff',
    }),
}));

jest.mock('@context/server', () => ({
    useServerUrl: () => 'https://test-server.com',
}));

jest.mock('expo-av', () => ({
    Audio: {
        Sound: {
            createAsync: jest.fn().mockResolvedValue({
                sound: {
                    getStatusAsync: jest.fn().mockResolvedValue({
                        isLoaded: true,
                        durationMillis: 60000,
                    }),
                    unloadAsync: jest.fn(),
                },
            }),
        },
    },
}));

jest.mock('@components/profile_picture', () => {
    return function MockProfilePicture() {
        return null;
    };
});

jest.mock('../../loading', () => {
    return function MockLoading() {
        return null;
    };
});

// Mock the AudioPlayController with different states for testing
const createMockAudioController = (currentTrack: string, currentPosition: number, playingState: string) => ({
    playAudio: jest.fn(),
    puseAudio: jest.fn(),
    seekToPosition: jest.fn(),
    changeCurrentSpeed: jest.fn(),
    playingState,
    currentTrack,
    recording: null,
    currentPosition,
    fileDuration: 60000,
    speedType: '1x',
});

jest.mock('@app/context/AudioPlayController', () => ({
    AudioPlayController: jest.fn(),
    enAudioState: {
        loading: 'loading',
        playing: 'playing',
        puse: 'puse',
        none: 'none',
        complate: 'complate',
        recoding: 'recoding',
    },
}));

describe('AudioFile Individual Message Isolation', () => {
    const mockFile1 = {
        id: 'audio-file-1',
        name: 'voice-message-1.m4a',
        mime_type: 'audio/m4a',
        size: 1024,
    } as any;

    const mockFile2 = {
        id: 'audio-file-2',
        name: 'voice-message-2.m4a',
        mime_type: 'audio/m4a',
        size: 2048,
    } as any;

    const mockAuthor = {
        id: 'user-id',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
    } as any;

    const baseProps = {
        disabled: false,
        isCurrentUser: false,
        author: mockAuthor,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Animation Isolation', () => {
        it('should maintain independent animation state for each component instance', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            
            // Mock first instance - playing
            AudioPlayController.mockReturnValueOnce(
                createMockAudioController('audio-file-1', 30000, 'playing')
            );
            
            const { rerender } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Mock second instance - not playing
            AudioPlayController.mockReturnValueOnce(
                createMockAudioController('audio-file-1', 30000, 'playing')
            );

            rerender(
                <>
                    <AudioFile {...baseProps} file={mockFile1} />
                    <AudioFile {...baseProps} file={mockFile2} />
                </>
            );

            // Both components should render without errors
            // Only the active component should have animations
            expect(AudioPlayController).toHaveBeenCalledTimes(2);
        });

        it('should reset animations when track becomes inactive', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            
            // Initially playing
            AudioPlayController.mockReturnValue(
                createMockAudioController('audio-file-1', 30000, 'playing')
            );
            
            const { rerender } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Switch to different track
            AudioPlayController.mockReturnValue(
                createMockAudioController('audio-file-2', 0, 'playing')
            );

            rerender(<AudioFile {...baseProps} file={mockFile1} />);

            // Component should handle the state change gracefully
            expect(AudioPlayController).toHaveBeenCalled();
        });
    });

    describe('Synchronization Accuracy', () => {
        it('should synchronize dot position with waveform progress', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            
            AudioPlayController.mockReturnValue(
                createMockAudioController('audio-file-1', 30000, 'playing')
            );

            const { getByTestId } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Both dot and waveform should use the same position calculation
            const expectedProgress = (30000 / 60000) * 100; // 50%
            expect(expectedProgress).toBe(50);
        });

        it('should handle drag-to-seek functionality correctly', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            const mockSeekToPosition = jest.fn();
            
            AudioPlayController.mockReturnValue({
                ...createMockAudioController('audio-file-1', 0, 'playing'),
                seekToPosition: mockSeekToPosition,
            });

            const { UNSAFE_getByType } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Find the Slider component
            const slider = UNSAFE_getByType(require('@react-native-community/slider').default);
            
            // Simulate drag to 75% position
            fireEvent(slider, 'onSlidingComplete', 75);

            // Should seek to correct position (75% of 60000ms = 45000ms)
            expect(mockSeekToPosition).toHaveBeenCalledWith(45000);
        });
    });

    describe('Local State Management', () => {
        it('should maintain independent countdown timers', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            
            // First instance at 30 seconds
            AudioPlayController.mockReturnValueOnce(
                createMockAudioController('audio-file-1', 30000, 'playing')
            );

            const { rerender } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Second instance at 15 seconds
            AudioPlayController.mockReturnValueOnce(
                createMockAudioController('audio-file-2', 15000, 'playing')
            );

            rerender(
                <>
                    <AudioFile {...baseProps} file={mockFile1} />
                    <AudioFile {...baseProps} file={mockFile2} />
                </>
            );

            // Each component should maintain its own countdown state
            expect(AudioPlayController).toHaveBeenCalledTimes(2);
        });

        it('should handle replay functionality independently', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            const mockPlayAudio = jest.fn();
            
            AudioPlayController.mockReturnValue({
                ...createMockAudioController('audio-file-1', 60000, 'complate'),
                playAudio: mockPlayAudio,
            });

            const { getByRole } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Find and press the play button
            const playButton = getByRole('button');
            fireEvent.press(playButton);

            // Should replay from beginning
            expect(mockPlayAudio).toHaveBeenCalledWith({ url: 'audio-file-1' }, 0);
        });
    });

    describe('Performance Optimizations', () => {
        it('should memoize bar levels to prevent regeneration', () => {
            const { AudioPlayController } = require('@app/context/AudioPlayController');
            
            AudioPlayController.mockReturnValue(
                createMockAudioController('audio-file-1', 0, 'none')
            );

            const { rerender } = render(
                <AudioFile {...baseProps} file={mockFile1} />
            );

            // Re-render should not regenerate bar levels
            rerender(<AudioFile {...baseProps} file={mockFile1} />);

            // Component should render consistently
            expect(AudioPlayController).toHaveBeenCalled();
        });
    });
});
