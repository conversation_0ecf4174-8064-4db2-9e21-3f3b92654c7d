import React from 'react';
import Svg, { Path } from 'react-native-svg';

const IsoIcon = ({ width = 53, height = 53 }) => (
  <Svg width={width} height={height} viewBox="0 0 53 53" fill="none">
    <Path
      d="M40.5781 50.5156H12.4219C11.1041 50.5156 9.84026 49.9921 8.90844 49.0603C7.97662 48.1285 7.45312 46.8647 7.45312 45.5469V7.45312C7.45312 6.13533 7.97662 4.87151 8.90844 3.93969C9.84026 3.00787 11.1041 2.48438 12.4219 2.48438H35.6094L45.5469 12.4219V45.5469C45.5469 46.8647 45.0234 48.1285 44.0916 49.0603C43.1597 49.9921 41.8959 50.5156 40.5781 50.5156Z"
      fill="#E8EDF3"
    />
    <Path
      d="M7.45312 37.2656H45.5469V45.5469C45.5469 46.8647 45.0234 48.1285 44.0916 49.0603C43.1597 49.9921 41.8959 50.5156 40.5781 50.5156H12.4219C11.1041 50.5156 9.84026 49.9921 8.90844 49.0603C7.97662 48.1285 7.45312 46.8647 7.45312 45.5469V37.2656Z"
      fill="#F23D4A"
    />
    <Path
      d="M45.5469 12.4219H40.5781C39.2603 12.4219 37.9965 11.8984 37.0647 10.9666C36.1329 10.0347 35.6094 8.77092 35.6094 7.45312V2.48438L45.5469 12.4219Z"
      fill="#BFCEDD"
    />
    <Path
      d="M26.5 10.7656C24.5346 10.7656 22.6132 11.3484 20.979 12.4404C19.3448 13.5323 18.0711 15.0844 17.319 16.9002C16.5668 18.716 16.37 20.7141 16.7535 22.6418C17.1369 24.5695 18.0833 26.3402 19.4731 27.73C20.8629 29.1198 22.6336 30.0662 24.5613 30.4497C26.489 30.8331 28.4871 30.6363 30.3029 29.8842C32.1188 29.132 33.6708 27.8583 34.7627 26.2241C35.8547 24.5899 36.4375 22.6686 36.4375 20.7031C36.4375 18.0675 35.3905 15.5399 33.5269 13.6763C31.6632 11.8126 29.1356 10.7656 26.5 10.7656ZM26.5 22.3594C26.1724 22.3594 25.8522 22.2622 25.5798 22.0802C25.3075 21.8983 25.0952 21.6396 24.9698 21.3369C24.8445 21.0343 24.8117 20.7013 24.8756 20.38C24.9395 20.0587 25.0972 19.7636 25.3289 19.532C25.5605 19.3003 25.8556 19.1426 26.1769 19.0787C26.4982 19.0148 26.8312 19.0476 27.1338 19.1729C27.4365 19.2983 27.6951 19.5106 27.8771 19.783C28.0591 20.0553 28.1563 20.3755 28.1563 20.7031C28.1563 21.1424 27.9818 21.5637 27.6712 21.8743C27.3605 22.1849 26.9393 22.3594 26.5 22.3594Z"
      fill="#F23D4A"
    />
    {/* Continue adding remaining Path elements similarly */}
    <Path
      d="M26.5 28.1562C24.524 28.154 22.6295 27.3681 21.2323 25.9708C19.835 24.5736 19.0491 22.6791 19.0469 20.7031C19.0469 20.4835 19.1341 20.2729 19.2894 20.1176C19.4447 19.9622 19.6554 19.875 19.875 19.875C20.0946 19.875 20.3053 19.9622 20.4606 20.1176C20.6159 20.2729 20.7031 20.4835 20.7031 20.7031C20.7049 22.24 21.3161 23.7135 22.4029 24.8002C23.4896 25.887 24.9631 26.4983 26.5 26.5C26.7196 26.5 26.9303 26.5872 27.0856 26.7426C27.2409 26.8979 27.3281 27.1085 27.3281 27.3281C27.3281 27.5478 27.2409 27.7584 27.0856 27.9137C26.9303 28.069 26.7196 28.1562 26.5 28.1562Z" 
      fill="#E8EDF3"
    />
    <Path d="M20.7031 41.4062C20.9228 41.4062 21.1334 41.319 21.2887 41.1637C21.444 41.0084 21.5312 40.7978 21.5312 40.5781C21.5312 40.3585 21.444 40.1479 21.2887 39.9926C21.1334 39.8372 20.9228 39.75 20.7031 39.75H17.3906C17.171 39.75 16.9604 39.8372 16.8051 39.9926C16.6497 40.1479 16.5625 40.3585 16.5625 40.5781C16.5625 40.7978 16.6497 41.0084 16.8051 41.1637C16.9604 41.319 17.171 41.4062 17.3906 41.4062H18.2188V46.375H17.3906C17.171 46.375 16.9604 46.4622 16.8051 46.6176C16.6497 46.7729 16.5625 46.9835 16.5625 47.2031C16.5625 47.4228 16.6497 47.6334 16.8051 47.7887C16.9604 47.944 17.171 48.0312 17.3906 48.0312H20.7031C20.9228 48.0312 21.1334 47.944 21.2887 47.7887C21.444 47.6334 21.5312 47.4228 21.5312 47.2031C21.5312 46.9835 21.444 46.7729 21.2887 46.6176C21.1334 46.4622 20.9228 46.375 20.7031 46.375H19.875V41.4062H20.7031Z" fill="white"/>
<Path d="M25.6719 41.4062H27.3281C27.5478 41.4062 27.7584 41.319 27.9137 41.1637C28.069 41.0084 28.1562 40.7978 28.1562 40.5781C28.1562 40.3585 28.069 40.1479 27.9137 39.9926C27.7584 39.8372 27.5478 39.75 27.3281 39.75H25.6719C25.013 39.75 24.3811 40.0117 23.9152 40.4777C23.4492 40.9436 23.1875 41.5755 23.1875 42.2344C23.1875 42.8933 23.4492 43.5252 23.9152 43.9911C24.3811 44.457 25.013 44.7188 25.6719 44.7188C25.8915 44.7188 26.1021 44.806 26.2574 44.9613C26.4128 45.1166 26.5 45.3272 26.5 45.5469C26.5 45.7665 26.4128 45.9771 26.2574 46.1324C26.1021 46.2878 25.8915 46.375 25.6719 46.375H24.0156C23.796 46.375 23.5854 46.4622 23.4301 46.6176C23.2747 46.7729 23.1875 46.9835 23.1875 47.2031C23.1875 47.4228 23.2747 47.6334 23.4301 47.7887C23.5854 47.944 23.796 48.0312 24.0156 48.0312H25.6719C26.3308 48.0312 26.9627 47.7695 27.4286 47.3036C27.8945 46.8377 28.1562 46.2058 28.1562 45.5469C28.1562 44.888 27.8945 44.2561 27.4286 43.7902C26.9627 43.3242 26.3308 43.0625 25.6719 43.0625C25.4522 43.0625 25.2416 42.9753 25.0863 42.8199C24.931 42.6646 24.8438 42.454 24.8438 42.2344C24.8438 42.0147 24.931 41.8041 25.0863 41.6488C25.2416 41.4935 25.4522 41.4062 25.6719 41.4062Z" fill="white"/>
<Path d="M33.125 39.75C31.2985 39.75 29.8125 41.6075 29.8125 43.8906C29.8125 46.1738 31.2985 48.0312 33.125 48.0312C34.9515 48.0312 36.4375 46.1738 36.4375 43.8906C36.4375 41.6075 34.9515 39.75 33.125 39.75ZM33.125 46.375C32.2272 46.375 31.4688 45.2374 31.4688 43.8906C31.4688 42.5439 32.2272 41.4062 33.125 41.4062C34.0228 41.4062 34.7812 42.5439 34.7812 43.8906C34.7812 45.2374 34.0228 46.375 33.125 46.375Z" fill="white"/>
  </Svg>
);

export default IsoIcon;
