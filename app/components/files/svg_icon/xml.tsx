import React from 'react';
import Svg, { Path } from 'react-native-svg';

const XmlIcon = ({ width = 53, height = 53 }) => (
  <Svg width={width} height={height} viewBox="0 0 53 53" fill="none">
    <Path d="M42.1094 52.4219H12.8906C11.5231 52.4219 10.2116 51.8786 9.24461 50.9116C8.27762 49.9447 7.73438 48.6331 7.73438 47.2656V7.73438C7.73438 6.36685 8.27762 5.05534 9.24461 4.08836C10.2116 3.12137 11.5231 2.57813 12.8906 2.57812H36.9531L47.2656 12.8906V47.2656C47.2656 48.6331 46.7224 49.9447 45.7554 50.9116C44.7884 51.8786 43.4769 52.4219 42.1094 52.4219Z"
      fill="#E8EDF3" />
    <Path d="M7.73438 38.6719H47.2656V47.2656C47.2656 48.6331 46.7224 49.9447 45.7554 50.9116C44.7884 51.8786 43.4769 52.4219 42.1094 52.4219H12.8906C11.5231 52.4219 10.2116 51.8786 9.24461 50.9116C8.27762 49.9447 7.73438 48.6331 7.73438 47.2656V38.6719Z" fill="#F23D4A" />
    <Path d="M47.2656 12.8906H42.1094C40.7419 12.8906 39.4303 12.3474 38.4634 11.3804C37.4964 10.4134 36.9531 9.1019 36.9531 7.73438V2.57812L47.2656 12.8906Z"
      fill="#BFCEDD" />
    <Path d="M20.625 29.2188C20.5122 29.2189 20.4004 29.1968 20.2961 29.1536C20.1918 29.1104 20.0971 29.047 20.0173 28.9671L14.8611 23.8108C14.7813 23.731 14.718 23.6363 14.6748 23.532C14.6316 23.4278 14.6094 23.316 14.6094 23.2031C14.6094 23.0903 14.6316 22.9785 14.6748 22.8743C14.718 22.77 14.7813 22.6753 14.8611 22.5955L20.0173 17.4392C20.1785 17.278 20.3971 17.1875 20.625 17.1875C20.853 17.1875 21.0715 17.278 21.2327 17.4392C21.3939 17.6004 21.4844 17.819 21.4844 18.0469C21.4844 18.2748 21.3939 18.4934 21.2327 18.6546L16.684 23.2031L21.2327 27.7517C21.3529 27.8719 21.4348 28.025 21.4679 28.1917C21.5011 28.3584 21.4841 28.5312 21.419 28.6883C21.354 28.8453 21.2438 28.9795 21.1025 29.074C20.9612 29.1684 20.795 29.2188 20.625 29.2188Z"
      fill="#F23D4A" />
    <Path d="M34.375 29.2188C34.2051 29.2188 34.0389 29.1684 33.8976 29.074C33.7562 28.9795 33.6461 28.8453 33.581 28.6883C33.516 28.5312 33.499 28.3584 33.5321 28.1917C33.5653 28.025 33.6472 27.8719 33.7673 27.7517L38.316 23.2031L33.7673 18.6546C33.6062 18.4934 33.5156 18.2748 33.5156 18.0469C33.5156 17.819 33.6062 17.6004 33.7673 17.4392C33.9285 17.278 34.1471 17.1875 34.375 17.1875C34.603 17.1875 34.8215 17.278 34.9827 17.4392L40.139 22.5955C40.2188 22.6753 40.2821 22.77 40.3253 22.8743C40.3685 22.9785 40.3907 23.0903 40.3907 23.2031C40.3907 23.316 40.3685 23.4278 40.3253 23.532C40.2821 23.6363 40.2188 23.731 40.139 23.8108L34.9827 28.9671C34.903 29.047 34.8083 29.1104 34.704 29.1536C34.5997 29.1968 34.4879 29.2189 34.375 29.2188Z"
      fill="#F23D4A" />
    <Path d="M27.5 24.0625C27.9746 24.0625 28.3594 23.6777 28.3594 23.2031C28.3594 22.7285 27.9746 22.3438 27.5 22.3438C27.0254 22.3438 26.6406 22.7285 26.6406 23.2031C26.6406 23.6777 27.0254 24.0625 27.5 24.0625Z" 
    fill="#F23D4A" />
    <Path d="M30.9375 24.0625C31.4121 24.0625 31.7969 23.6777 31.7969 23.2031C31.7969 22.7285 31.4121 22.3438 30.9375 22.3438C30.4629 22.3438 30.0781 22.7285 30.0781 23.2031C30.0781 23.6777 30.4629 24.0625 30.9375 24.0625Z" 
    fill="#F23D4A" />
    <Path d="M24.0625 24.0625C24.5371 24.0625 24.9219 23.6777 24.9219 23.2031C24.9219 22.7285 24.5371 22.3438 24.0625 22.3438C23.5879 22.3438 23.2031 22.7285 23.2031 23.2031C23.2031 23.6777 23.5879 24.0625 24.0625 24.0625Z" 
    fill="#F23D4A" />
    <Path d="M36.9531 48.125H34.375V42.1094C34.375 41.8815 34.2845 41.6629 34.1233 41.5017C33.9621 41.3405 33.7435 41.25 33.5156 41.25C33.2877 41.25 33.0691 41.3405 32.908 41.5017C32.7468 41.6629 32.6562 41.8815 32.6562 42.1094V48.9844C32.6562 49.2123 32.7468 49.4309 32.908 49.592C33.0691 49.7532 33.2877 49.8438 33.5156 49.8438H36.9531C37.181 49.8438 37.3996 49.7532 37.5608 49.592C37.722 49.4309 37.8125 49.2123 37.8125 48.9844C37.8125 48.7565 37.722 48.5379 37.5608 48.3767C37.3996 48.2155 37.181 48.125 36.9531 48.125Z" 
    fill="white" />
    <Path d="M21.8688 41.3407C21.665 41.2388 21.429 41.222 21.2127 41.2941C20.9965 41.3661 20.8178 41.5211 20.7158 41.725L19.7657 43.6253L18.8155 41.725C18.7125 41.523 18.5339 41.3698 18.3185 41.299C18.1031 41.2281 17.8684 41.2453 17.6656 41.3467C17.4628 41.4481 17.3082 41.6256 17.2357 41.8404C17.1632 42.0553 17.1785 42.2901 17.2783 42.4937L18.8049 45.5468L17.2783 48.6C17.2268 48.701 17.1958 48.8113 17.1871 48.9244C17.1784 49.0374 17.1922 49.1511 17.2276 49.2589C17.263 49.3666 17.3195 49.4663 17.3936 49.5521C17.4677 49.6379 17.5581 49.7082 17.6596 49.759C17.761 49.8097 17.8715 49.8398 17.9846 49.8477C18.0978 49.8555 18.2114 49.8408 18.3188 49.8046C18.4263 49.7683 18.5255 49.7111 18.6108 49.6363C18.696 49.5615 18.7656 49.4705 18.8155 49.3687L19.7657 47.4684L20.7158 49.3687C20.7658 49.4705 20.8354 49.5615 20.9206 49.6363C21.0059 49.7111 21.1051 49.7683 21.2126 49.8046C21.32 49.8408 21.4336 49.8555 21.5467 49.8477C21.6599 49.8398 21.7704 49.8097 21.8718 49.759C21.9732 49.7082 22.0636 49.6379 22.1378 49.5521C22.2119 49.4663 22.2683 49.3666 22.3038 49.2589C22.3392 49.1511 22.353 49.0374 22.3443 48.9244C22.3356 48.8113 22.3046 48.701 22.253 48.6L20.7265 45.5468L22.253 42.4937C22.355 42.2898 22.3718 42.0539 22.2997 41.8376C22.2276 41.6214 22.0726 41.4427 21.8688 41.3407Z" 
    fill="white" />
    <Path d="M30.2509 41.2678C30.057 41.2278 29.8552 41.2561 29.6798 41.3478C29.5044 41.4395 29.366 41.5891 29.2881 41.7711L27.5 45.9435L25.7119 41.7711C25.6339 41.5891 25.4956 41.4396 25.3201 41.3479C25.1447 41.2561 24.943 41.2277 24.7491 41.2675C24.5552 41.3073 24.381 41.4129 24.2559 41.5663C24.1308 41.7197 24.0625 41.9116 24.0625 42.1095V48.9845C24.0625 49.2125 24.153 49.4311 24.3142 49.5922C24.4754 49.7534 24.694 49.8439 24.9219 49.8439C25.1498 49.8439 25.3684 49.7534 25.5295 49.5922C25.6907 49.4311 25.7812 49.2125 25.7812 48.9845V46.2964L26.71 48.4637C26.7762 48.6183 26.8864 48.7501 27.0268 48.8427C27.1673 48.9353 27.3318 48.9847 27.5 48.9847C27.6682 48.9847 27.8327 48.9353 27.9732 48.8427C28.1136 48.7501 28.2238 48.6183 28.29 48.4637L29.2188 46.2964V48.9845C29.2188 49.2125 29.3093 49.4311 29.4705 49.5922C29.6316 49.7534 29.8502 49.8439 30.0781 49.8439C30.306 49.8439 30.5246 49.7534 30.6858 49.5922C30.847 49.4311 30.9375 49.2125 30.9375 48.9845V42.1095C30.9375 41.9116 30.8691 41.7198 30.744 41.5664C30.6189 41.4131 30.4447 41.3076 30.2509 41.2678Z"
     fill="white" />
  </Svg>
);

export default XmlIcon;
