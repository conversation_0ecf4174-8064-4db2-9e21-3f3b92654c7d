import React from 'react';
import Svg, { Path } from 'react-native-svg';

const RarIcon = ({ width = 53, height = 53 }) => (
  <Svg width={width} height={height} viewBox="0 0 53 53" fill="none">
    <Path d="M49.875 31.1719V38.2969C49.875 39.7753 48.6816 40.9688 47.2031 40.9688H9.79688C8.31844 40.9688 7.125 39.7753 7.125 38.2969V31.1719C7.125 29.6934 8.31844 28.5 9.79688 28.5H47.2031C48.6816 28.5 49.875 29.6934 49.875 31.1719Z" fill="#E8EDF3"/>
<Path d="M39.1875 25.8281V30.2812C39.1875 31.7597 37.9941 32.9531 36.5156 32.9531H20.4844C19.0059 32.9531 17.8125 31.7597 17.8125 30.2812V25.8281H21.375V29.3906H35.625V25.8281H39.1875Z" fill="#0082AA"/>
<Path d="M47.2031 28.5H9.79688C8.32111 28.5 7.125 27.3039 7.125 25.8281V18.7031C7.125 17.2274 8.32111 16.0312 9.79688 16.0312H47.2031C48.6789 16.0312 49.875 17.2274 49.875 18.7031V25.8281C49.875 27.3039 48.6789 28.5 47.2031 28.5Z" fill="#F23D4A"/>
<Path d="M47.2031 16.0312H9.79688C8.32111 16.0312 7.125 14.8351 7.125 13.3594V6.23438C7.125 4.75861 8.32111 3.5625 9.79688 3.5625H47.2031C48.6789 3.5625 49.875 4.75861 49.875 6.23438V13.3594C49.875 14.8351 48.6789 16.0312 47.2031 16.0312Z" fill="#E8EDF3"/>
<Path d="M21.375 3.5625H35.625V40.9688H21.375V3.5625Z" fill="#9EB4C6"/>
<Path d="M28.5 40.9688C24.5652 40.9688 21.375 37.7785 21.375 33.8438V30.2812H35.625V33.8438C35.625 37.7785 32.4348 40.9688 28.5 40.9688Z" fill="#688AA6"/>
<Path d="M28.5 39.1875C24.5652 39.1875 21.375 35.9973 21.375 32.0625V28.5H35.625V32.0625C35.625 35.9973 32.4348 39.1875 28.5 39.1875Z" fill="#9EB4C6"/>
<Path d="M21.375 29.3906H35.625V32.9531H21.375V29.3906Z" fill="#688AA6"/>
<Path d="M39.1875 15.1406V29.3906C39.1875 30.8691 37.9941 32.0625 36.5156 32.0625H20.4844C19.0059 32.0625 17.8125 30.8691 17.8125 29.3906V15.1406C17.8125 13.6622 19.0059 12.4688 20.4844 12.4688H21.375V28.5H35.625V12.4688H36.5156C37.9941 12.4688 39.1875 13.6622 39.1875 15.1406Z" fill="#BFCEDD"/>
<Path d="M30.2812 27.6094V29.3906C30.2812 30.3703 29.4797 31.1719 28.5 31.1719C27.5203 31.1719 26.7188 30.3703 26.7188 29.3906V27.6094H30.2812Z" fill="#D2D2D2"/>
<Path d="M28.5 30.2812C27.5159 30.2812 26.7188 29.4841 26.7188 28.5V21.375C26.7188 20.3909 27.5159 19.5938 28.5 19.5938C29.4841 19.5938 30.2812 20.3909 30.2812 21.375V28.5C30.2812 29.4841 29.4841 30.2812 28.5 30.2812Z" fill="#F0F0F0"/>
<Path d="M28.5 17.8125C29.4838 17.8125 30.2812 17.015 30.2812 16.0312C30.2812 15.0475 29.4838 14.25 28.5 14.25C27.5162 14.25 26.7188 15.0475 26.7188 16.0312C26.7188 17.015 27.5162 17.8125 28.5 17.8125Z" fill="#C80A50"/>
<Path d="M28.5 10.6875C29.4838 10.6875 30.2812 9.89001 30.2812 8.90625C30.2812 7.92249 29.4838 7.125 28.5 7.125C27.5162 7.125 26.7188 7.92249 26.7188 8.90625C26.7188 9.89001 27.5162 10.6875 28.5 10.6875Z" fill="#E8EDF3"/>
<Path d="M30.2812 16.0312H26.7188C26.7188 15.0516 27.5203 14.25 28.5 14.25C29.4797 14.25 30.2812 15.0516 30.2812 16.0312Z" fill="#F0F0F0"/>
<Path d="M41.8594 53.4375H15.1406C14.1565 53.4375 13.3594 52.6404 13.3594 51.6562V39.1875C13.3594 38.2034 14.1565 37.4062 15.1406 37.4062H41.8594C42.8435 37.4062 43.6406 38.2034 43.6406 39.1875V51.6562C43.6406 52.6404 42.8435 53.4375 41.8594 53.4375Z" fill="#F23D4A"/>
<Path d="M31.1719 47.2031H25.8281C25.3365 47.2031 24.9375 46.8041 24.9375 46.3125V43.6406C24.9375 41.6759 26.5353 40.0781 28.5 40.0781C30.4647 40.0781 32.0625 41.6759 32.0625 43.6406V46.3125C32.0625 46.8041 31.6635 47.2031 31.1719 47.2031ZM26.7188 45.4219H30.2812V43.6406C30.2812 42.6583 29.4824 41.8594 28.5 41.8594C27.5176 41.8594 26.7188 42.6583 26.7188 43.6406V45.4219Z" fill="#F0F0F0"/>
<Path d="M25.8281 50.7656C25.3365 50.7656 24.9375 50.3666 24.9375 49.875V46.3125C24.9375 45.8209 25.3365 45.4219 25.8281 45.4219C26.3197 45.4219 26.7188 45.8209 26.7188 46.3125V49.875C26.7188 50.3666 26.3197 50.7656 25.8281 50.7656Z" fill="#F0F0F0"/>
<Path d="M31.1719 50.7656C30.6803 50.7656 30.2812 50.3666 30.2812 49.875V46.3125C30.2812 45.8209 30.6803 45.4219 31.1719 45.4219C31.6635 45.4219 32.0625 45.8209 32.0625 46.3125V49.875C32.0625 50.3666 31.6635 50.7656 31.1719 50.7656Z" fill="#F0F0F0"/>
<Path d="M37.4062 47.2031H34.7344C34.2428 47.2031 33.8438 46.8041 33.8438 46.3125V40.9688C33.8438 40.4771 34.2428 40.0781 34.7344 40.0781H37.4062C39.371 40.0781 40.9688 41.6759 40.9688 43.6406C40.9688 45.6053 39.371 47.2031 37.4062 47.2031ZM35.625 45.4219H37.4062C38.3886 45.4219 39.1875 44.623 39.1875 43.6406C39.1875 42.6583 38.3886 41.8594 37.4062 41.8594H35.625V45.4219Z" fill="#F0F0F0"/>
<Path d="M34.7344 50.7656C34.2428 50.7656 33.8438 50.3666 33.8438 49.875V46.3125C33.8438 45.8209 34.2428 45.4219 34.7344 45.4219C35.226 45.4219 35.625 45.8209 35.625 46.3125V49.875C35.625 50.3666 35.226 50.7656 34.7344 50.7656Z" fill="#F0F0F0"/>
<Path d="M40.079 50.7658C39.7522 50.7658 39.4378 50.5859 39.281 50.2733L37.4998 46.7108C37.2798 46.2708 37.4579 45.7355 37.8979 45.5156C38.3388 45.2947 38.8731 45.4737 39.0931 45.9137L40.8744 49.4762C41.0943 49.9161 40.9162 50.4514 40.4763 50.6714C40.3489 50.7355 40.2126 50.7658 40.079 50.7658Z" fill="#F0F0F0"/>
<Path d="M19.5938 47.2031H16.9219C16.4303 47.2031 16.0312 46.8041 16.0312 46.3125V40.9688C16.0312 40.4771 16.4303 40.0781 16.9219 40.0781H19.5938C21.5585 40.0781 23.1562 41.6759 23.1562 43.6406C23.1562 45.6053 21.5585 47.2031 19.5938 47.2031ZM17.8125 45.4219H19.5938C20.5761 45.4219 21.375 44.623 21.375 43.6406C21.375 42.6583 20.5761 41.8594 19.5938 41.8594H17.8125V45.4219Z" fill="#F0F0F0"/>
<Path d="M16.9219 50.7656C16.4303 50.7656 16.0312 50.3666 16.0312 49.875V46.3125C16.0312 45.8209 16.4303 45.4219 16.9219 45.4219C17.4135 45.4219 17.8125 45.8209 17.8125 46.3125V49.875C17.8125 50.3666 17.4135 50.7656 16.9219 50.7656Z" fill="#F0F0F0"/>
<Path d="M22.2665 50.7658C21.9397 50.7658 21.6253 50.5859 21.4685 50.2733L19.6873 46.7108C19.4673 46.2708 19.6454 45.7355 20.0854 45.5156C20.5263 45.2947 21.0606 45.4737 21.2806 45.9137L23.0619 49.4762C23.2818 49.9161 23.1037 50.4514 22.6638 50.6714C22.5364 50.7355 22.4001 50.7658 22.2665 50.7658Z" fill="#F0F0F0"/>
</Svg>
);

export default RarIcon;
