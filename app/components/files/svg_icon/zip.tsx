import React from 'react';
import Svg, { Path } from 'react-native-svg';

const ZipIcon = ({ width = 53, height = 53 }) => (
  <Svg width={width} height={height} viewBox="0 0 53 53" fill="none">
   <Path d="M49.4 40.8499V54.1499C49.3985 54.6534 49.1978 55.1358 48.8418 55.4917C48.4858 55.8477 48.0034 56.0484 47.5 56.0499H9.49998C8.99653 56.0484 8.51413 55.8477 8.15813 55.4917C7.80214 55.1358 7.60148 54.6534 7.59998 54.1499V40.8499L8.54998 39.8999H48.45L49.4 40.8499Z" fill="#F23D4A"/>
<Path d="M49.4 13.3002V40.8502H7.59998V2.8502C7.60148 2.34676 7.80214 1.86436 8.15813 1.50836C8.51413 1.15237 8.99653 0.951708 9.49998 0.950204H37.05C37.1135 0.948385 37.177 0.951561 37.24 0.959704C37.809 0.966947 38.3709 1.08744 38.8928 1.31416C39.4147 1.54088 39.8863 1.86928 40.28 2.2802L48.07 10.0702C48.4809 10.4639 48.8093 10.9354 49.036 11.4574C49.2627 11.9793 49.3832 12.5412 49.3905 13.1102C49.3986 13.1732 49.4018 13.2367 49.4 13.3002Z" fill="#E8EDF3"/>
<Path d="M49.3905 13.11C49.3422 12.6418 49.1223 12.2081 48.7732 11.8924C48.4242 11.5766 47.9706 11.4012 47.5 11.4H41.8C41.0441 11.4 40.3192 11.0997 39.7847 10.5652C39.2503 10.0307 38.95 9.30584 38.95 8.54997V2.84997C38.9487 2.37932 38.7733 1.92578 38.4576 1.57672C38.1419 1.22767 37.7082 1.0078 37.24 0.959473C37.809 0.966716 38.3709 1.08721 38.8928 1.31393C39.4148 1.54065 39.8863 1.86905 40.28 2.27997L48.07 10.07C48.4809 10.4636 48.8093 10.9352 49.036 11.4571C49.2628 11.9791 49.3832 12.541 49.3905 13.11Z" fill="#BFCEDD"/>
<Path d="M32.2999 32.3095C32.2989 32.8475 32.1837 33.3792 31.9618 33.8693C31.7399 34.3594 31.4165 34.7969 31.013 35.1527C30.6094 35.5084 30.1349 35.7745 29.6208 35.9332C29.1068 36.0919 28.5649 36.1396 28.031 36.0732C27.4971 36.0068 26.9834 35.8278 26.5238 35.548C26.0643 35.2683 25.6694 34.8941 25.3653 34.4503C25.0612 34.0065 24.8548 33.5031 24.7597 32.9736C24.6647 32.4441 24.6832 31.9004 24.8139 31.3785L26.5999 24.7V23.75H30.3999V24.7L32.1859 31.3785C32.2607 31.6832 32.299 31.9957 32.2999 32.3095Z" fill="#BFCEDD"/>
<Path d="M28.5 33.2499C28.2481 33.2499 28.0065 33.1498 27.8283 32.9717C27.6501 32.7935 27.55 32.5519 27.55 32.2999V31.3499C27.55 31.0979 27.6501 30.8563 27.8283 30.6782C28.0065 30.5 28.2481 30.3999 28.5 30.3999C28.752 30.3999 28.9936 30.5 29.1718 30.6782C29.35 30.8563 29.45 31.0979 29.45 31.3499V32.2999C29.45 32.5519 29.35 32.7935 29.1718 32.9717C28.9936 33.1498 28.752 33.2499 28.5 33.2499Z" fill="#9EB4C6"/>
<Path d="M29.4499 9.5002H30.3999C30.6519 9.5002 30.8935 9.40011 31.0717 9.22195C31.2498 9.04379 31.3499 8.80215 31.3499 8.5502C31.3499 8.29824 31.2498 8.0566 31.0717 7.87844C30.8935 7.70028 30.6519 7.6002 30.3999 7.6002H29.4499V5.7002H30.3999C30.6519 5.7002 30.8935 5.60011 31.0717 5.42195C31.2498 5.24379 31.3499 5.00215 31.3499 4.7502C31.3499 4.49824 31.2498 4.2566 31.0717 4.07844C30.8935 3.90028 30.6519 3.8002 30.3999 3.8002H29.4499V0.950195H27.5499V5.7002H26.5999C26.3479 5.7002 26.1063 5.80028 25.9282 5.97844C25.75 6.1566 25.6499 6.39824 25.6499 6.6502C25.6499 6.90215 25.75 7.14379 25.9282 7.32195C26.1063 7.50011 26.3479 7.6002 26.5999 7.6002H27.5499V9.5002H26.5999C26.3479 9.5002 26.1063 9.60028 25.9282 9.77844C25.75 9.9566 25.6499 10.1982 25.6499 10.4502C25.6499 10.7022 25.75 10.9438 25.9282 11.1219C26.1063 11.3001 26.3479 11.4002 26.5999 11.4002H27.5499V13.3002H26.5999C26.3479 13.3002 26.1063 13.4003 25.9282 13.5784C25.75 13.7566 25.6499 13.9982 25.6499 14.2502C25.6499 14.5022 25.75 14.7438 25.9282 14.9219C26.1063 15.1001 26.3479 15.2002 26.5999 15.2002H27.5499V20.9002H29.4499V17.1002H30.3999C30.6519 17.1002 30.8935 17.0001 31.0717 16.8219C31.2498 16.6438 31.3499 16.4022 31.3499 16.1502C31.3499 15.8982 31.2498 15.6566 31.0717 15.4784C30.8935 15.3003 30.6519 15.2002 30.3999 15.2002H29.4499V13.3002H30.3999C30.6519 13.3002 30.8935 13.2001 31.0717 13.0219C31.2498 12.8438 31.3499 12.6022 31.3499 12.3502C31.3499 12.0982 31.2498 11.8566 31.0717 11.6784C30.8935 11.5003 30.6519 11.4002 30.3999 11.4002H29.4499V9.5002Z" fill="#BFCEDD"/>
<Path d="M32.3001 45.6002C32.5521 45.6002 32.7937 45.5001 32.9719 45.3219C33.1501 45.1438 33.2501 44.9022 33.2501 44.6502C33.2501 44.3982 33.1501 44.1566 32.9719 43.9784C32.7937 43.8003 32.5521 43.7002 32.3001 43.7002H26.6001C26.3482 43.7002 26.1066 43.8003 25.9284 43.9784C25.7502 44.1566 25.6501 44.3982 25.6501 44.6502C25.6501 44.9022 25.7502 45.1438 25.9284 45.3219C26.1066 45.5001 26.3482 45.6002 26.6001 45.6002H28.5001V51.3002H26.6001C26.3482 51.3002 26.1066 51.4003 25.9284 51.5784C25.7502 51.7566 25.6501 51.9982 25.6501 52.2502C25.6501 52.5022 25.7502 52.7438 25.9284 52.9219C26.1066 53.1001 26.3482 53.2002 26.6001 53.2002H32.3001C32.5521 53.2002 32.7937 53.1001 32.9719 52.9219C33.1501 52.7438 33.2501 52.5022 33.2501 52.2502C33.2501 51.9982 33.1501 51.7566 32.9719 51.5784C32.7937 51.4003 32.5521 51.3002 32.3001 51.3002H30.4001V45.6002H32.3001Z" fill="white"/>
<Path d="M22.8 51.3002H19L23.56 45.2202C23.6659 45.0791 23.7303 44.9112 23.7462 44.7355C23.762 44.5598 23.7286 44.3831 23.6497 44.2253C23.5708 44.0675 23.4495 43.9348 23.2995 43.8421C23.1494 43.7493 22.9765 43.7002 22.8 43.7002H17.1C16.8481 43.7002 16.6064 43.8003 16.4283 43.9784C16.2501 44.1566 16.15 44.3982 16.15 44.6502C16.15 44.9022 16.2501 45.1438 16.4283 45.3219C16.6064 45.5001 16.8481 45.6002 17.1 45.6002H20.9L16.34 51.6802C16.2342 51.8213 16.1697 51.9892 16.1539 52.1649C16.138 52.3406 16.1714 52.5172 16.2503 52.675C16.3292 52.8328 16.4505 52.9656 16.6006 53.0583C16.7507 53.1511 16.9236 53.2002 17.1 53.2002H22.8C23.052 53.2002 23.2936 53.1001 23.4718 52.9219C23.6499 52.7438 23.75 52.5022 23.75 52.2502C23.75 51.9982 23.6499 51.7566 23.4718 51.5784C23.2936 51.4003 23.052 51.3002 22.8 51.3002Z" fill="white"/>
<Path d="M38.9501 43.7002H36.1001C35.8482 43.7002 35.6066 43.8003 35.4284 43.9784C35.2502 44.1566 35.1501 44.3982 35.1501 44.6502V52.2502C35.1501 52.5022 35.2502 52.7438 35.4284 52.9219C35.6066 53.1001 35.8482 53.2002 36.1001 53.2002C36.3521 53.2002 36.5937 53.1001 36.7719 52.9219C36.9501 52.7438 37.0501 52.5022 37.0501 52.2502V49.4002H38.9501C39.706 49.4002 40.4309 49.0999 40.9654 48.5654C41.4999 48.031 41.8001 47.3061 41.8001 46.5502C41.8001 45.7943 41.4999 45.0694 40.9654 44.5349C40.4309 44.0005 39.706 43.7002 38.9501 43.7002ZM38.9501 47.5002H37.0501V45.6002H38.9501C39.2021 45.6002 39.4437 45.7003 39.6219 45.8784C39.8001 46.0566 39.9001 46.2982 39.9001 46.5502C39.9001 46.8022 39.8001 47.0438 39.6219 47.2219C39.4437 47.4001 39.2021 47.5002 38.9501 47.5002Z" fill="white"/>
<Path d="M30.3999 19.9502H26.5999C26.0752 19.9502 25.6499 20.3755 25.6499 20.9002V23.7502C25.6499 24.2749 26.0752 24.7002 26.5999 24.7002H30.3999C30.9246 24.7002 31.3499 24.2749 31.3499 23.7502V20.9002C31.3499 20.3755 30.9246 19.9502 30.3999 19.9502Z" fill="#9EB4C6"/>
</Svg>
);

export default ZipIcon;
