import React from 'react';
import Svg, { G, <PERSON>, ClipPath, Defs } from 'react-native-svg';

const SvgIcon = () => (
  <Svg width="53" height="53" viewBox="0 0 53 53" fill="none">
    <Defs>
      <ClipPath id="clip0_2808_3856">
        <Path d="M0 0h53v53H0z" />
      </ClipPath>
    </Defs>
    <G clipPath="url(#clip0_2808_3856)">
      <Path
        d="M10.8464 44.2715V38.8887H12.9623L47.6224 38.8804V47.5891C47.6228 48.2995 47.4833 49.0031 47.2117 49.6595C46.9401 50.316 46.5419 50.9125 46.0397 51.4149C45.5375 51.9174 44.9412 52.316 44.2849 52.588C43.6286 52.8599 42.9252 52.9999 42.2148 52.9999H16.2562C15.5459 53 14.8425 52.8603 14.1863 52.5886C13.53 52.3169 12.9337 51.9186 12.4313 51.4165C11.929 50.9143 11.5305 50.3181 11.2585 49.662C10.9866 49.0058 10.8466 48.3025 10.8464 47.5922V44.2715Z"
        fill="#E8EDF3"
      />
      <Path
        d="M47.6223 37.8866V38.8824L12.9622 38.8907H8.069C7.40358 38.8907 6.76174 39.1372 6.26738 39.5826C5.77302 40.028 5.46119 40.6408 5.39209 41.3026L5.4366 22.3085C5.4366 21.5947 5.72016 20.9101 6.2249 20.4054C6.72963 19.9007 7.4142 19.6171 8.12801 19.6171H10.8463L47.6223 19.6099V37.8866Z"
        fill="#F23D4A"
      />
      <Path
        d="M46.5336 7.68478C47.077 8.16948 47.4415 8.82283 47.5687 9.53979H41.1321C40.2718 9.53951 39.4468 9.19763 38.8384 8.58928C38.2301 7.98094 37.8882 7.15593 37.8879 6.29561V0.245117C38.2269 0.382575 38.5398 0.576869 38.8134 0.819629L46.5336 7.68478Z"
        fill="#BFCEDD"
      />
      <Path
        d="M47.5685 9.54C47.6026 9.72786 47.6196 9.91841 47.6193 10.1093V19.611L10.8433 19.6183V6.4894C10.8434 5.63689 11.0115 4.79275 11.3379 4.00522C11.6643 3.21768 12.1427 2.50216 12.7457 1.89954C13.3487 1.29692 14.0646 0.818992 14.8523 0.493065C15.64 0.167137 16.4843 -0.000407167 17.3368 7.43043e-07H36.6538C37.0776 -0.000246395 37.4973 0.083123 37.8888 0.245333V6.29582C37.8891 7.15597 38.2308 7.98082 38.8389 8.58913C39.447 9.19745 40.2718 9.53945 41.1319 9.54H47.5685Z"
        fill="#E8EDF3"
      />
      {/* Add remaining paths here in similar format */}
<Path d="M10.8464 39.1229V44.2718H8.06912C7.71463 44.2718 7.36364 44.2018 7.03629 44.0657C6.70894 43.9297 6.41169 43.7304 6.1616 43.4791C5.91152 43.2279 5.71353 42.9297 5.57901 42.6017C5.44449 42.2738 5.37608 41.9225 5.37771 41.568C5.37774 41.4794 5.38258 41.391 5.39221 41.303C5.46155 40.6413 5.77348 40.0288 6.26781 39.5836C6.76215 39.1384 7.40386 38.892 8.06912 38.8921H10.8464V39.1229Z" fill="#B10C18"/>
<Path d="M36.3724 34.7811C35.5265 34.7808 34.6924 34.5823 33.9371 34.2013C33.1818 33.8204 32.5264 33.2677 32.0233 32.5876C31.5203 31.9075 31.1836 31.119 31.0405 30.2853C30.8973 29.4515 30.9516 28.5959 31.1989 27.7869C31.4463 26.978 31.8798 26.2383 32.4647 25.6272C33.0497 25.0161 33.7697 24.5506 34.5671 24.2682C35.3645 23.9857 36.2169 23.8941 37.0561 24.0007C37.8953 24.1072 38.6978 24.4091 39.3992 24.8819C39.6362 25.042 39.7998 25.2896 39.8542 25.5703C39.9085 25.851 39.8491 26.1418 39.6891 26.3788C39.529 26.6157 39.2814 26.7793 39.0007 26.8337C38.72 26.888 38.4292 26.8287 38.1922 26.6686C37.77 26.3845 37.2871 26.2032 36.7822 26.1394C36.2773 26.0756 35.7645 26.1311 35.2849 26.3013C34.8053 26.4715 34.3722 26.7517 34.0205 27.1195C33.6687 27.4873 33.4081 27.9324 33.2594 28.4191C33.1108 28.9058 33.0783 29.4206 33.1645 29.9222C33.2507 30.4237 33.4533 30.8981 33.756 31.3072C34.0586 31.7163 34.453 32.0488 34.9074 32.278C35.3617 32.5072 35.8635 32.6267 36.3724 32.627C37.8755 32.627 38.6373 31.6022 38.9137 30.448H37.5214C37.3772 30.4524 37.2335 30.4279 37.0989 30.3758C36.9643 30.3236 36.8416 30.245 36.738 30.1446C36.6344 30.0441 36.552 29.9239 36.4957 29.791C36.4394 29.658 36.4105 29.5152 36.4105 29.3709C36.4105 29.2265 36.4394 29.0837 36.4957 28.9508C36.552 28.8179 36.6344 28.6976 36.738 28.5972C36.8416 28.4967 36.9643 28.4181 37.0989 28.366C37.2335 28.3139 37.3772 28.2893 37.5214 28.2938H40.1145C40.4 28.2938 40.6739 28.4072 40.8758 28.6091C41.0777 28.811 41.1911 29.0848 41.1911 29.3704C41.1942 32.5566 39.2098 34.7811 36.3724 34.7811Z" fill="white"/>
<Path d="M15.8928 34.7811C14.4301 34.7811 13.0027 34.1683 12.0731 33.1415C11.9779 33.0366 11.9043 32.9141 11.8565 32.7809C11.8087 32.6476 11.7876 32.5063 11.7945 32.3649C11.8013 32.2235 11.8359 32.0848 11.8963 31.9568C11.9567 31.8288 12.0418 31.7139 12.1466 31.6187C12.2514 31.5236 12.3739 31.45 12.5072 31.4022C12.6404 31.3544 12.7818 31.3333 12.9232 31.3401C13.0646 31.3469 13.2032 31.3816 13.3312 31.442C13.4593 31.5024 13.5741 31.5874 13.6693 31.6922C14.1972 32.275 15.0285 32.6239 15.8928 32.6239C16.7334 32.6239 17.3255 32.1208 17.4124 31.6332C17.5284 30.9842 16.6536 30.5867 16.2737 30.4459C14.928 29.9501 13.7366 29.419 13.6859 29.3973C13.6339 29.3737 13.5837 29.3464 13.5358 29.3155C13.0924 29.0277 12.7413 28.6183 12.5245 28.1362C12.3077 27.6542 12.2344 27.1199 12.3132 26.5972C12.4799 25.442 13.3246 24.501 14.5181 24.1418C16.8835 23.4286 18.8306 25.0331 18.9124 25.1024C19.132 25.2856 19.2699 25.5484 19.2957 25.8332C19.3215 26.118 19.2332 26.4014 19.05 26.621C18.8669 26.8406 18.6041 26.9785 18.3193 27.0044C18.0345 27.0302 17.7511 26.9418 17.5315 26.7587C17.4963 26.7297 16.3628 25.8374 15.1403 26.2059C14.5802 26.3746 14.4684 26.7514 14.4467 26.9057C14.4287 27.0112 14.4391 27.1196 14.4767 27.2198C14.5144 27.32 14.578 27.4085 14.661 27.476C14.9653 27.6096 15.957 28.0371 17.0222 28.4305C19.2063 29.2348 19.7539 30.8113 19.5386 32.0173C19.2488 33.6176 17.7157 34.7811 15.8928 34.7811Z" fill="white"/>
<Path d="M25.7703 34.7814C25.5065 34.781 25.2493 34.6987 25.0342 34.5459C24.8191 34.3932 24.6567 34.1774 24.5695 33.9284L21.6048 25.3883C21.5198 25.1207 21.5425 24.8305 21.6683 24.5794C21.7941 24.3283 22.0129 24.1362 22.2782 24.0441C22.5434 23.9519 22.8342 23.9669 23.0886 24.086C23.3429 24.205 23.5407 24.4186 23.6399 24.6813L25.7527 30.767L27.764 24.7114C27.8621 24.4506 28.0574 24.2379 28.3089 24.118C28.5604 23.9981 28.8485 23.9802 29.1129 24.0682C29.3773 24.1562 29.5973 24.3432 29.7268 24.5899C29.8563 24.8366 29.8852 25.1239 29.8074 25.3915L26.9793 33.9118V33.9222C26.8931 34.1724 26.7312 34.3895 26.516 34.5436C26.3009 34.6977 26.0432 34.7812 25.7786 34.7824L25.7703 34.7814Z" fill="white"/>

    </G>
  </Svg>
);

export default SvgIcon;
