// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import FormattedDate from '@components/formatted_date';
import { useTheme } from '@context/theme';
import { getFormattedFileSize } from '@utils/file';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';
import { Audio } from 'expo-av';
import useIntl from 'react-intl/src/components/useIntl';

type FileInfoProps = {
    disabled?: boolean;
    file: FileInfo;
    showDate: boolean;
    isCurrentUser?: boolean | null;
    channelName?: string;
    onPress: () => void;
    isFromSearch?: boolean | null;
    acknolowgmentFile?: React.JSX.Element | undefined;

}
const FORMAT = ' • MMM DD HH:MM A';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {

        fileDownloadContainer: {
            flexDirection: 'row',
            marginTop: 3,
            //alignItems: 'flex-start',
            //justifyContent:'flex-'
            justifyContent: 'flex-start',
            //backgroundColor: 'red',



        },
        fileStatsContainer: {
            flexGrow: 1,
            flexDirection: 'row',


        },


        channelWrapper: {
            flexShrink: 1,
            marginRight: 4,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 4,
            borderRadius: 4,
        },

    };
});

const FileInfo = ({ disabled, file, channelName, showDate, onPress,
    isCurrentUser, isFromSearch = false, acknolowgmentFile = undefined

}: FileInfoProps) => {
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const intl = useIntl();





    // console.log(`\n\n\n this the file uri ${file.uri}\n`)
    // console.log(` this the file local ${file.localPath}\n\n\n`)
    return (
        <View style={{
            flex: 1,
            // justifyContent: 'flex-end',
            flexDirection: 'column',
            // backgroundColor:'red',
            alignItems:  'flex-end',
           // marginEnd:isFromSearch?5:0,
                                    width:'100%'

        }}>
            <TouchableOpacity
                disabled={disabled}
                onPress={onPress}
                style={{
                    flex: 1,
                                   width:'100%'

                }}
            >
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                        width:'100%'
,

                    }}
                >
                    <Text
                        numberOfLines={isFromSearch ? null : 2}
                        style={{
                                                //             backgroundColor:'red'
                        width:'100%'
,

                            textAlign:isFromSearch?'left':'right',
                            marginTop: 5,
                            flexDirection: 'column',
                            color: isCurrentUser ? 'white' : changeOpacity(theme.sidebarText, 0.70),
                            paddingRight: 4,
                            ...typography('Heading', 200, 'SemiBold'),
                        }}
                    >

                        {file.name || ''}
                    </Text>

                    {/* <Text
                        numberOfLines={1}
                        ellipsizeMode='tail'
                        style={{

                            marginTop: -4,
                            color: isCurrentUser ? 'white' : changeOpacity(theme.sidebarText, 0.70),
                            paddingRight: 0,
                            ...typography('Heading', 300, 'SemiBold'),
                        }}
                    >

                        {
                            file.extension
                            //decodeURIComponent(file.name.trim())
                        }
                    </Text>*/}

                </View>

                <View style={[style.fileDownloadContainer,{marginTop:5}]}  >
                    <View style={{
                        flexGrow: 1,
                        flexDirection: 'row',
                        justifyContent:isFromSearch?'flex-start': 'flex-end',
                        marginStart: 5

                    }}>
                        <Text

                            style={{
                                //color: isCurrentUser ? changeOpacity(theme.centerChannelBg, 0.70) : changeOpacity(theme.sidebarText, 0.70),
                                color: isCurrentUser ?"white" : changeOpacity(theme.sidebarText, 0.70),
                                ...typography('Heading', 75),
                                marginEnd: 3
                            }}>
                            {file.extension?.toUpperCase() || ''}
                        </Text>

                        <Text style={{
                            //  color: isCurrentUser ? changeOpacity(theme.centerChannelBg, 0.70) : changeOpacity(theme.sidebarText, 0.70),
                            color: isCurrentUser ?"white" : changeOpacity(theme.sidebarText, 0.70),
                            ...typography('Heading', 75),
                        }}>
                            {`${getFormattedFileSize(file.size || 0)}`}
                        </Text>

                        {showDate &&
                            <FormattedDate
                                style={{
                                    //color: isCurrentUser ? 'white' : changeOpacity(theme.sidebarText, 0.70),
                                    color:  isCurrentUser ?"white" : changeOpacity(theme.sidebarText, 0.70),
                                    ...typography('Heading', 75, 'Light'),
                                }}
                                format={FORMAT}
                                value={file.create_at as number}
                            />
                        }
                        {//isCurrentUser&&acknolowgmentFile&&acknolowgmentFile
                        }
                    </View>
                    {channelName &&
                        <View style={style.channelWrapper}>
                            <Text
                                style={{
                                    ...typography('Heading', 50, 'SemiBold'),
                                    // color: changeOpacity(theme.sidebarText, 0.70),
                                    color:  isCurrentUser ?"white" : changeOpacity(theme.sidebarText, 0.70),

                                    // color: isCurrentUser ? 'white' : changeOpacity(theme.sidebarText, 0.70),
                                }}
                                numberOfLines={1}
                            >
                                {channelName === "Off-Topic" ? "المجموعة الفرعية" : channelName === "Town Square" ? "المجموعة الرئيسية" : channelName}

                                {/* {channelName} */}
                            </Text>
                        </View>
                    }

                </View>
            </TouchableOpacity>

        </View>
    );
};

export default FileInfo;
