import React, {
    useEffect,
    useRef,
    useState,
    use<PERSON><PERSON>back,
    useMemo,
} from "react";
import {
    Dimensions,
    Text,
    TouchableOpacity,
    View,
    Animated,
    Easing,
} from "react-native";
import { useTheme } from "@context/theme";
import { Audio } from "expo-av";
import { PauseIcon, PlayIcon } from "react-native-heroicons/mini";
import {
    AudioPlayController,
    enAudioState,
} from "@app/context/AudioPlayController";
import { useServerUrl } from "@context/server";
import { MicrophoneIcon } from "react-native-heroicons/outline";
import {
    GestureHandlerRootView,
    PanGestureHandler,
    State as GestureState
} from "react-native-gesture-handler";
import ProfilePicture from "@components/profile_picture";
import Slider from "@react-native-community/slider";
import Loading from "../loading";
import ReanimatedAnimated, {
    useSharedValue,
    useAnimatedStyle,
    runOnJS,
    withSpring,
    withTiming,
} from "react-native-reanimated";
import { changeOpacity } from "@utils/theme";
import MediaMessageMetadata from "@components/media_message_metadata";

import type UserModel from "@typings/database/models/servers/user";
import type PostModel from "@typings/database/models/servers/post";

// generate random bar heights per-instance - memoized to prevent regeneration
const makeLevels = (n: number) =>
    Array.from({ length: n }, () => Math.floor(Math.random() * 11) + 12);

type AudioFileProps = {
    disabled?: boolean;
    file: FileInfo;
    isCurrentUser?: boolean;
    author?: any;
    acknolowgment?: string | React.JSX.Element;
    // New props for timestamp and read receipts
    post?: PostModel;
    currentUser?: UserModel;
    acknowledgementsVisible?: boolean;
    hasReactions?: boolean;
    location?: string;
    showTimestamp?: boolean;
    showReadReceipts?: boolean;
};

const AudioFile: React.FC<AudioFileProps> = React.memo(
    ({
        disabled,
        file,
        isCurrentUser = false,
        author,
        acknolowgment,
        post,
        currentUser,
        acknowledgementsVisible = false,
        hasReactions = false,
        location = "",
        showTimestamp = true,
        showReadReceipts = true,
    }) => {
        const theme = useTheme();
        const serverUrl = useServerUrl();
        const audioCtrl = AudioPlayController();
        const windowWidth = Dimensions.get("window").width;
        const defaultWidth = windowWidth < 400 ? 290 : 306;
        const waveformWidth = defaultWidth - 96; // width for the slider

        // Calculate proper playback head boundaries to prevent overflow
        // Account for: marginHorizontal (10px each side), dot width (18px), and safe margins
        const dotWidth = 18;
        const containerMargin = 20; // 10px marginHorizontal on each side
        const safeMargin = 20; // Additional margin to prevent edge overflow
        const playbackHeadMaxWidth = waveformWidth - containerMargin - safeMargin;

        // --- controller state ---
        const {
            playAudio,
            playingState,
            currentTrack,
            currentPosition,
            seekToPosition,
            fileDuration: controllerFileDuration,
        } = audioCtrl;

        const isActive = currentTrack === file.id;
        const isPlaying = isActive && playingState === enAudioState.playing;
        const isLoading = isActive && playingState === enAudioState.loading;

        // --- component state ---
        const [fileDuration, setFileDuration] = useState(0);
        const [countdown, setCountdown] = useState("0:00");

        // --- per-instance state for isolated animations ---
        const [localPosition, setLocalPosition] = useState(0);
        const [isDragging, setIsDragging] = useState(false);

        // --- Reanimated shared values for smooth seeking ---
        const seekPosition = useSharedValue(0);
        const playbackHeadPosition = useSharedValue(0);
        const waveformProgress = useSharedValue(0);

        // --- per-instance animated values for waveform bars & text fade ---
        // Memoize the bar levels to prevent regeneration on re-renders
        const barLevels = useMemo(() => makeLevels(20), []);
        const barScales = useRef(
            barLevels.map(() => new Animated.Value(1))
        ).current;
        const textFade = useRef(new Animated.Value(0.5)).current;

        // --- utility ---
        const fmt = (ms: number) => {
            if (ms <= 0) return "0:00";
            const s = Math.floor(ms / 1000);
            return `${Math.floor(s / 60)}:${(s % 60)
                .toString()
                .padStart(2, "0")}`;
        };

        // load duration
        useEffect(() => {
            (async () => {
                try {
                    const { sound } = await Audio.Sound.createAsync(
                        { uri: `${serverUrl}/api/v4/files/${file.id}` },
                        { shouldPlay: false }
                    );
                    const st = await sound.getStatusAsync();
                    if (st.isLoaded && st.durationMillis) {
                        setFileDuration(st.durationMillis);
                        setCountdown(fmt(st.durationMillis));
                    }
                    await sound.unloadAsync();
                } catch {
                    setFileDuration(0);
                    setCountdown("0:00");
                }
            })();
        }, [file.id]);

        // fade duration text
        useEffect(() => {
            Animated.timing(textFade, {
                toValue: isPlaying ? 1 : 0.5,
                duration: isPlaying ? 500 : 300,
                easing: Easing.out(Easing.ease),
                useNativeDriver: true,
            }).start();
        }, [isPlaying]);

        // Sync local position with global controller position for this specific track
        // Update more frequently for better responsiveness
        useEffect(() => {
            if (isActive && !isDragging) {
                setLocalPosition(currentPosition);
            }
        }, [isActive, currentPosition, isDragging]);

        // Additional effect for more frequent position updates during playback
        // Only update position for the currently active track to prevent interference
        useEffect(() => {
            if (!isActive || !isPlaying || isDragging) return;

            const interval = setInterval(() => {
                // Only update position for the active track to maintain isolation
                if (isActive) {
                    setLocalPosition(currentPosition);
                }
            }, 50); // Update every 50ms for smoother animation

            return () => clearInterval(interval);
        }, [isActive, isPlaying, isDragging, currentPosition]);

        // update countdown timer - only for active track to prevent cross-component interference
        useEffect(() => {
            const dur = controllerFileDuration || fileDuration;
            if (dur > 0 && isActive) {
                // Only update countdown for the currently active track
                // This prevents other voice message components from being affected
                setCountdown(fmt(Math.max(0, dur - localPosition)));
            }
        }, [localPosition, fileDuration, controllerFileDuration, isActive]);

        // Smooth waveform animation - continuous fill without jumping
        useEffect(() => {
            if (!isActive || !isPlaying || isDragging) return;

            let raf: number;
            const update = () => {
                const dur = controllerFileDuration || fileDuration;
                if (dur <= 0) return;

                // Calculate smooth progress for continuous animation
                const progress = Math.min(Math.max(localPosition / dur, 0), 1);
                const barCount = barScales.length;

                // Apply smooth, water-like animation to bars
                barScales.forEach((bv, i) => {
                    const barProgress = i / (barCount - 1); // Normalize bar position (0 to 1)

                    if (progress >= barProgress) {
                        // Bar is fully filled - smooth scale animation
                        const targetScale = 1.1 + Math.sin(Date.now() * 0.01 + i * 0.5) * 0.1;
                        Animated.timing(bv, {
                            toValue: targetScale,
                            duration: 100,
                            easing: Easing.out(Easing.ease),
                            useNativeDriver: true,
                        }).start();
                    } else if (progress >= barProgress - (1 / barCount)) {
                        // Bar is at the edge - partial animation for smooth transition
                        const edgeProgress = (progress - (barProgress - (1 / barCount))) / (1 / barCount);
                        const targetScale = 1 + (edgeProgress * 0.2);
                        Animated.timing(bv, {
                            toValue: targetScale,
                            duration: 50,
                            easing: Easing.out(Easing.ease),
                            useNativeDriver: true,
                        }).start();
                    } else {
                        // Bar is not reached yet - reset to normal
                        Animated.timing(bv, {
                            toValue: 1,
                            duration: 100,
                            easing: Easing.out(Easing.ease),
                            useNativeDriver: true,
                        }).start();
                    }
                });

                raf = requestAnimationFrame(update);
            };

            raf = requestAnimationFrame(update);
            return () => cancelAnimationFrame(raf);
        }, [
            isActive,
            isPlaying,
            isDragging,
            localPosition,
            fileDuration,
            controllerFileDuration,
            barScales,
        ]);

        // Helper function to reset this specific voice message state
        const resetVoiceMessageState = useCallback(
            (duration: number) => {
                if (duration > 0) {
                    // Reset countdown timer to full duration for this specific instance
                    setCountdown(fmt(duration));

                    // Reset animations to initial state for this specific instance
                    barScales.forEach((bv) => {
                        Animated.timing(bv, {
                            toValue: 1,
                            duration: 200,
                            easing: Easing.out(Easing.ease),
                            useNativeDriver: true,
                        }).start();
                    });

                    // Reset position for this specific instance
                    setLocalPosition(0);
                }
            },
            [barScales]
        );

        // Track previous state to detect transitions for this specific component
        const prevPlayingState = useRef(playingState);
        const prevIsActive = useRef(isActive);

        // Reset logic - only affects this specific voice message instance
        useEffect(() => {
            const currentDuration = controllerFileDuration || fileDuration;

            // Only reset if this specific voice message's state changed
            if (isActive) {
                // Case 1: Audio completion (natural end) - only for this specific track
                if (
                    playingState === enAudioState.complate &&
                    prevPlayingState.current !== enAudioState.complate
                ) {
                    resetVoiceMessageState(currentDuration);
                }
                // Case 2: Manual stop/pause - only for this specific track
                else if (
                    !isPlaying &&
                    [enAudioState.none, enAudioState.puse].includes(
                        playingState
                    ) &&
                    prevPlayingState.current === enAudioState.playing
                ) {
                    // For pause, don't reset position - keep current position for resume
                    if (playingState === enAudioState.puse) {
                        setCountdown(
                            fmt(Math.max(0, currentDuration - localPosition))
                        );
                        // Smoothly reset bar animations for pause state
                        barScales.forEach((bv) => {
                            Animated.timing(bv, {
                                toValue: 1,
                                duration: 150,
                                easing: Easing.out(Easing.ease),
                                useNativeDriver: true,
                            }).start();
                        });
                    } else {
                        // For stop, reset everything
                        resetVoiceMessageState(currentDuration);
                    }
                }
            } else {
                // Case 3: This track becomes inactive (another track starts playing)
                // Only reset if this track was previously active
                if (prevIsActive.current === true) {
                    resetVoiceMessageState(currentDuration);
                }
            }

            // Update previous state references
            prevPlayingState.current = playingState;
            prevIsActive.current = isActive;
        }, [
            isActive,
            isPlaying,
            playingState,
            fileDuration,
            controllerFileDuration,
            localPosition,
            resetVoiceMessageState,
        ]);

        // play/pause/replay
        const onPress = useCallback(async () => {
            if (isActive && playingState === enAudioState.complate) {
                if (file.id) {
                    await playAudio({ url: file.id }, 0);
                }
            } else {
                if (file.id) {
                    await playAudio({ url: file.id }, localPosition || 0);
                }
            }
        }, [
            isActive,
            playingState,
            currentPosition,
            controllerFileDuration,
            fileDuration,
            file.id,
            playAudio,
        ]);

        // bar color - smooth gradient-like fill for water-like animation
        const barColor = useCallback(
            (i: number) => {
                const base = isCurrentUser
                    ? changeOpacity(theme.sidebarText, 0.4)
                    : "#ccc";
                if (!isActive) return base;
                const dur = controllerFileDuration || fileDuration;
                if (dur <= 0) return base;

                // Calculate smooth progress for continuous color transition
                const progress = Math.min(Math.max(localPosition / dur, 0), 1);
                const barProgress = i / (barScales.length - 1);

                // Create smooth color transition with gradient effect
                if (progress >= barProgress) {
                    // Fully filled bars
                    return isCurrentUser ? "white" : theme.buttonBg;
                } else if (progress >= barProgress - (1 / barScales.length)) {
                    // Transition zone - create gradient effect
                    const edgeProgress = (progress - (barProgress - (1 / barScales.length))) / (1 / barScales.length);

                    // Interpolate between base and fill color for smooth transition
                    if (isCurrentUser) {
                        const opacity = 0.4 + (edgeProgress * 0.6);
                        return changeOpacity("white", opacity);
                    } else {
                        const opacity = 0.3 + (edgeProgress * 0.7);
                        return changeOpacity(theme.buttonBg, opacity);
                    }
                } else {
                    // Unfilled bars
                    return base;
                }
            },
            [
                isActive,
                localPosition,
                controllerFileDuration,
                fileDuration,
                isCurrentUser,
                theme.buttonBg,
                theme.sidebarText,
                barScales.length,
            ]
        );

        // play/pause icon
        const icon = useMemo(() => {
            if (isLoading)
                return (
                    <Loading
                        size={28}
                        color={isCurrentUser ? "white" : theme.buttonBg}
                    />
                );
            return isPlaying ? (
                <PauseIcon
                    size={25}
                    color={isCurrentUser ? "white" : "#808887"}
                />
            ) : (
                <PlayIcon
                    size={25}
                    color={isCurrentUser ? "white" : "#808887"}
                />
            );
        }, [isPlaying, isLoading, isCurrentUser, theme.buttonBg]);

        return (
            <GestureHandlerRootView>
                <View
                    style={{
                        flexDirection: "row-reverse",
                        alignItems: "center",
                        justifyContent: "space-between",
                        width: defaultWidth,
                        height: 60,
                        paddingEnd: 20,
                    }}
                >
                    {/* Avatar */}
                    <View
                        style={{
                            width: 50,
                            height: 50,
                            borderRadius: 30,
                            overflow: "hidden",
                            marginTop: -1,
                            marginLeft: 5,
                        }}
                    >
                        {author ? (
                            <ProfilePicture
                                author={author}
                                size={50}
                                showStatus={false}
                            />
                        ) : (
                            <View
                                style={{
                                    width: 50,
                                    height: 50,
                                    backgroundColor: isCurrentUser
                                        ? changeOpacity(
                                              theme.centerChannelBg,
                                              0.3
                                          )
                                        : changeOpacity(theme.sidebarText, 0.3),
                                    borderRadius: 27.5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <MicrophoneIcon
                                    size={25}
                                    color={
                                        isCurrentUser
                                            ? "white"
                                            : theme.centerChannelBg
                                    }
                                />
                            </View>
                        )}
                    </View>

                    {/* Mic overlay */}
                    <View
                        style={{ position: "absolute", bottom: 5, start: 218 }}
                    >
                        <MicrophoneIcon
                            size={20}
                            color={
                                isCurrentUser
                                    ? "white"
                                    : changeOpacity(theme.sidebarText, 0.3)
                            }
                        />
                    </View>

                    {/* Waveform + Slider-dot */}
                    <View style={{ flex: 1, marginHorizontal: 10, top: -13 }}>
                        <View style={{ position: "relative", top: 25 }}>
                            <View
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "space-between",
                                }}
                            >
                                {barScales.map((bv, i) => (
                                    <Animated.View
                                        key={i}
                                        style={{
                                            width: 4,
                                            height: barLevels[i],
                                            borderRadius: 5,
                                            backgroundColor: barColor(i),
                                            transform: [{ scaleY: bv }],
                                        }}
                                    />
                                ))}
                            </View>

                            {/* Slider thumb as the dot */}
                            <Slider
                                style={{
                                    position: "absolute",
                                    top: -5,
                                    start: -11,
                                    width: waveformWidth,
                                    height: 30,
                                    zIndex: 50,
                                }}
                                minimumValue={0}
                                maximumValue={100}
                                value={
                                    (controllerFileDuration || fileDuration) > 0
                                        ? (localPosition /
                                              (controllerFileDuration ||
                                                  fileDuration)) *
                                          100
                                        : 0
                                }
                                minimumTrackTintColor="transparent"
                                maximumTrackTintColor="transparent"
                                thumbTintColor="transparent" // Hide default thumb to use custom dot
                                onValueChange={(pct) => {
                                    // live feedback while dragging
                                    setIsDragging(true);
                                    const dur =
                                        controllerFileDuration || fileDuration;
                                    const newPos = dur * (pct / 100);

                                    // Update local position immediately for smooth feedback
                                    setLocalPosition(newPos);

                                    // Update shared values for smooth animations
                                    seekPosition.value = newPos;
                                    playbackHeadPosition.value = withTiming(pct, { duration: 50 });
                                    waveformProgress.value = withTiming(pct / 100, { duration: 50 });
                                }}
                                onSlidingComplete={(pct) => {
                                    const dur =
                                        controllerFileDuration || fileDuration;
                                    const newTime = dur * (pct / 100);

                                    // Update position and seek
                                    setLocalPosition(newTime);
                                    setIsDragging(false);

                                    // Seek to the new position if this track is active
                                    if (isActive) {
                                        seekToPosition(newTime);
                                    }

                                    // Final animation update
                                    seekPosition.value = newTime;
                                    playbackHeadPosition.value = withSpring(pct);
                                    waveformProgress.value = withSpring(pct / 100);
                                }}
                            />

                            {/* Custom larger playback head dot */}
                            <View
                                style={{
                                    position: "absolute",
                                    top: 1,
                                    left: (() => {
                                        const duration = controllerFileDuration || fileDuration;
                                        if (duration <= 0) return 0;

                                        // Calculate position percentage (0 to 1)
                                        const progress = Math.min(Math.max(localPosition / duration, 0), 1);

                                        // Calculate position within safe boundaries
                                        // Start from half dot width to center it properly
                                        const dotHalfWidth = dotWidth / 2;
                                        const maxPosition = playbackHeadMaxWidth - dotHalfWidth;
                                        const position = progress * maxPosition;

                                        // Ensure position stays within bounds
                                        return Math.max(0, Math.min(position, maxPosition));
                                    })(),
                                    width: dotWidth,
                                    height: dotWidth,
                                    borderRadius: dotWidth / 2,
                                    backgroundColor: isCurrentUser
                                        ? "white"
                                        : theme.buttonBg,
                                    borderWidth: 2,
                                    borderColor: isCurrentUser
                                        ? theme.buttonBg
                                        : "white",
                                    zIndex: 100,
                                    pointerEvents: "none", // Allow touch events to pass through to slider
                                }}
                            />
                        </View>

                        {/* Countdown and Metadata Container */}
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginTop: 8,
                                paddingHorizontal: 8,
                                width: "100%", // Use full width for proper layout
                            }}
                        >
                            {/* Countdown Timer */}
                            <Animated.Text
                                style={{
                                    color: isCurrentUser ? "white" : "#808887",
                                    fontSize: 14,
                                    opacity: textFade,
                                    top: 19,
                                    right: 10,
                                    fontFamily: "IBMPlexSansArabic-Medium",
                                }}
                            >
                                {countdown}
                            </Animated.Text>

                            {/* Timestamp and Read Receipts */}
                            {post && (
                                <MediaMessageMetadata
                                    post={post}
                                    currentUser={currentUser}
                                    isCurrentUser={isCurrentUser}
                                    acknowledgementsVisible={
                                        acknowledgementsVisible
                                    }
                                    hasReactions={hasReactions}
                                    location={location}
                                    showTimestamp={showTimestamp}
                                    showReadReceipts={showReadReceipts}
                                    textColor={
                                        isCurrentUser
                                            ? "white"
                                            : changeOpacity(
                                                  theme.sidebarText,
                                                  0.7
                                              )
                                    }
                                    iconColor={
                                        isCurrentUser
                                            ? "white"
                                            : theme.sidebarText
                                    }
                                    iconSize={16}
                                    containerStyle={{
                                        backgroundColor: changeOpacity(
                                            "black",
                                            0.6
                                        ),
                                        borderRadius: 8,
                                        paddingHorizontal: 6,
                                        paddingVertical: 2,
                                        top: 19,
                                        left: 20,
                                    }}
                                />
                            )}
                        </View>

                        {acknolowgment && typeof acknolowgment === "string" && (
                            <Text
                                style={{
                                    color: isCurrentUser ? "white" : "#808887",
                                }}
                            >
                                {acknolowgment}
                            </Text>
                        )}
                    </View>

                    {/* Play/Pause */}
                    <TouchableOpacity
                        disabled={disabled || isLoading}
                        onPress={onPress}
                        style={{
                            width: 30,
                            height: 30,
                            marginRight: 1,
                            marginStart: 10,
                            borderRadius: 20,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        {icon}
                    </TouchableOpacity>
                </View>
            </GestureHandlerRootView>
        );
    }
);

export default AudioFile;
