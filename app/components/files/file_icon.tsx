// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { View, StyleSheet } from 'react-native';
import IsoIcon from './svg_icon/iso';
import BatIcon from './svg_icon/bat';
import ZipIcon from './svg_icon/zip';
import RarIcon from './svg_icon/rar';
import SvgIcon from './svg_icon/svg';
import XmlIcon from './svg_icon/xml';
import ExeIcon from './svg_icon/exe';
import CompassIcon from '@components/compass_icon';
import { useTheme } from '@context/theme';
import { getFileType } from '@utils/file';
import Icon from '@jitsi/react-native-sdk/react/features/base/icons/components/Icon';

type FileIconProps = {
    backgroundColor?: string;
    defaultImage?: boolean;
    failed?: boolean;
    file?: FileInfo | ExtractedFileInfo;
    iconColor?: string;
    iconSize?: number;
    smallImage?: boolean;
    isFromUpload?:Boolean|undefined
}

const BLUE_ICON = '#338AFF';
const RED_ICON = '#ED522A';
const GREEN_ICON = '#1CA660';
const GRAY_ICON = '#999999';
const FAILED_ICON_NAME_AND_COLOR = ['file-image-broken-outline-large', GRAY_ICON];
const ICON_NAME_AND_COLOR_FROM_FILE_TYPE: Record<string, string[]> = {
    audio: ['file-audio-outline-large', BLUE_ICON],
    code: ['file-code-outline-large', BLUE_ICON],
    image: ['file-image-outline-large', BLUE_ICON],
    smallImage: ['image-outline', BLUE_ICON],
    other: ['file-generic-outline-large', BLUE_ICON],
    patch: ['file-patch-outline-large', BLUE_ICON],
    pdf: ['file-pdf-outline-large', RED_ICON],
    presentation: ['file-powerpoint-outline-large', RED_ICON],
    spreadsheet: ['file-excel-outline-large', GREEN_ICON],
    text: ['file-text-outline-large', GRAY_ICON],
    video: ['file-video-outline-large', BLUE_ICON],
    word: ['file-word-outline-large', BLUE_ICON],
    zip: ['file-zip-outline-large', BLUE_ICON],
    rar: ['rar', BLUE_ICON],
    exe: ['exe', GREEN_ICON],
};

const styles = StyleSheet.create({
    fileIconWrapper: {
        borderRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

const FileIcon = ({
    backgroundColor, defaultImage = false, failed = false, file,
    iconColor, iconSize = 48, smallImage = false,isFromUpload=true
}: FileIconProps) => {
    const theme = useTheme();
    const getFileIconNameAndColor = () => {
        if (failed) {
            return FAILED_ICON_NAME_AND_COLOR;
        }

        if (defaultImage) {
            if (smallImage) {
                return ICON_NAME_AND_COLOR_FROM_FILE_TYPE.smallImage;
            }

            return ICON_NAME_AND_COLOR_FROM_FILE_TYPE.image;
        }

        if (file) {
            const fileType = getFileType(file);
            return ICON_NAME_AND_COLOR_FROM_FILE_TYPE[fileType] || ICON_NAME_AND_COLOR_FROM_FILE_TYPE.other;
        }

        return ICON_NAME_AND_COLOR_FROM_FILE_TYPE.other;
    };

    const [iconName, defaultIconColor] = getFileIconNameAndColor();
    const color = iconColor || defaultIconColor;
    const bgColor = backgroundColor || theme?.centerChannelBg || 'transparent';
    return (
        <View >

            {file && getFileType(file) === 'bat' ? (
                <View style={[styles.fileIconWrapper,{top:isFromUpload?2:0}]}>
                    <BatIcon  />
                </View>
            ) : file && getFileType(file) === 'zip' ? (
                <View style={[
                    styles.fileIconWrapper,{top:isFromUpload?-8:-3}
                    ]}>
                    <ZipIcon width={iconSize} />
                </View>
            ) : file && getFileType(file) === 'iso' ? (
                <View style={[styles.fileIconWrapper,{top:isFromUpload?-8:-2}]}>
                    <IsoIcon width={iconSize} />
                </View>
            ) : file && getFileType(file) === 'rar' ? (
                <View style={[styles.fileIconWrapper,{top:isFromUpload?-8:-2}]}>
                    <RarIcon width={iconSize} />
                </View>
            ) : file && getFileType(file) === 'svg' ? (
                <View style={[styles.fileIconWrapper,{top:isFromUpload?-18:0}]}>
                    {/* <SvgIcon /> */}
                       <CompassIcon
                        name={iconName}
                        size={iconSize}
                        color={color}
                    />
                </View>
            ) : file && getFileType(file) === 'xml' ? (
                <View style={[styles.fileIconWrapper]}>
                    <XmlIcon
                        width={iconSize} />
                </View>
            ) : file && getFileType(file) === 'exe' ? (
                <View style={[styles.fileIconWrapper,{top:isFromUpload?-8:-2}]}>
                    <ExeIcon width={iconSize} />
                </View>
            ) : (
                
                <View style={[styles.fileIconWrapper, { backgroundColor: bgColor }]}>
                    <CompassIcon
                        name={iconName}
                        size={iconSize}
                        color={color}
                    />
                </View>
            )}
        </View>
    );
};

export default FileIcon;
