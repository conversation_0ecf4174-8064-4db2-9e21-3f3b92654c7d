// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {memo} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';

import Post from '@components/post_list/post';

import ChannelInfo from './channel_info';

import type PostModel from '@typings/database/models/servers/post';
import type {SearchPattern} from '@typings/global/markdown';

type Props = {
    appsEnabled: boolean;
    customEmojiNames: string[];
    isCRTEnabled: boolean;
    post: PostModel;
    location: string;
    testID?: string;
    searchPatterns?: SearchPattern[];
    skipSavedPostsHighlight?: boolean;
    isSaved?: boolean;
    fromResult?: boolean|undefined;
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 0,

    },
    content: {
        flexDirection: 'row',
        paddingBottom: 8,
    },
});

function PostWithChannelInfo({
    appsEnabled, 
    customEmojiNames,
     isCRTEnabled,
      post, location, 
      testID, searchPatterns, 
      skipSavedPostsHighlight = false, 
      isSaved,fromResult=false}: Props) {
 const windowWidth = Dimensions.get('window').width;

    return (
        <View style={styles.container}>
           
            <View style={{
                width:windowWidth,
                display:'flex',
                
            }}>
            {/*<ChannelInfo
                post={post}
                testID={`${testID}.post_channel_info.${post.id}`}
            />
            */}

            </View>
            <View style={styles.content}>
                <Post 
                fromResult={fromResult}
                isInSearch={true}
                    appsEnabled={appsEnabled}
                    customEmojiNames={customEmojiNames}
                    isCRTEnabled={isCRTEnabled}
                    post={post}
                    location={location}
                    highlightPinnedOrSaved={!skipSavedPostsHighlight}
                    searchPatterns={searchPatterns}
                    skipPinnedHeader={true}
                    skipSavedHeader={skipSavedPostsHighlight}
                    shouldRenderReplyButton={false}
                    showAddReaction={false}
                    previousPost={undefined}
                    nextPost={undefined}
                    testID={`${testID}.post`}
                    isSaved={isSaved}
                />
            </View>
        </View>
    );
}

export default memo(PostWithChannelInfo);
