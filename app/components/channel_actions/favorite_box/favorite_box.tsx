// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useState } from 'react';
import { useIntl } from 'react-intl';

import { toggleFavoriteChannel } from '@actions/remote/category';
import OptionBox from '@components/option_box';
import { useServerUrl } from '@context/server';
import { dismissBottomSheet } from '@screens/navigation';

import type { StyleProp, ViewStyle } from 'react-native';
import { changeOpacity, makeStyleSheetFromTheme } from '@app/utils/theme';
import Loading from '@app/components/loading';
import { useTheme } from '@app/context/theme';

type Props = {
    channelId: string;
    containerStyle?: StyleProp<ViewStyle>;
    isFavorited: boolean;
    showSnackBar?: boolean;
    testID?: string;
    isFromHome?: boolean | null;
}


const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        alignItems: 'center',
        backgroundColor: changeOpacity(theme.buttonBg, 0.08),
        borderRadius: 4,
        //flex: 1,
        // maxHeight: OPTIONS_HEIGHT,
        // justifyContent: 'center',
        minWidth: 50,
        paddingTop: 15,
        paddingBottom: 15,
        marginButton: 10,
        top: -10

    },

}));
const FavoriteBox = ({ channelId, containerStyle, isFavorited, showSnackBar = false, testID, isFromHome = false }: Props) => {
    const intl = useIntl();
    const serverUrl = useServerUrl();
    const [connecting, setConnecting] = useState(false);
  const theme = useTheme();
    const styles = getStyleSheet(theme);
   
    const handleOnPress = useCallback(async () => {
        setConnecting(true);

        await toggleFavoriteChannel(serverUrl, channelId, showSnackBar);
        setConnecting(false);

        dismissBottomSheet();
    }, [serverUrl, channelId, showSnackBar]);

    const favoriteActionTestId = isFavorited ? `${testID}.unfavorite.action` : `${testID}.favorite.action`;

    if ( connecting) {
        return (
            <Loading
                color={theme.buttonBg}
                size={'small'}
                footerText={""}
                containerStyle={styles.container}
                footerTextStyles={{}}
            />
        );
    }

    return (
        <OptionBox
            isFromHome={isFromHome}
            activeIconName='star'
            activeText={intl.formatMessage({ id: 'channel_info.favorited', defaultMessage: 'Favorited' })}
            containerStyle={containerStyle}
            iconName='star-outline'
            isActive={isFavorited}
            onPress={handleOnPress}
            testID={favoriteActionTestId}
            text={intl.formatMessage({ id: 'channel_info.favorite', defaultMessage: 'Favorite' })}
        />
    );
};

export default FavoriteBox;
