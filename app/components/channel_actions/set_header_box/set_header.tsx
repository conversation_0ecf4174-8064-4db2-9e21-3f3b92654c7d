// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback} from 'react';
import {useIntl} from 'react-intl';

import OptionBox from '@components/option_box';
import {Screens} from '@constants';
import {dismissBottomSheet, goToScreen, showModal} from '@screens/navigation';

import type {StyleProp, ViewStyle} from 'react-native';
import { useTheme } from '@app/context/theme';

type Props = {
    channelId: string;
    containerStyle?: StyleProp<ViewStyle>;
    isHeaderSet: boolean;
    inModal?: boolean;
    testID?: string;
    isFromHome?: boolean|null;
}

const SetHeaderBox = ({channelId, containerStyle, isHeaderSet, inModal, testID,isFromHome=false}: Props) => {
    const intl = useIntl();
    const theme = useTheme()

    const onSetHeader = useCallback(async () => {
        const title = intl.formatMessage({id: 'screens.channel_edit_header', defaultMessage: 'Edit Channel Header'});
        if (inModal) {
            goToScreen(Screens.CREATE_OR_EDIT_CHANNEL, title, {channelId, headerOnly: true},
                {
            layout: {
                componentBackgroundColor: theme.centerChannelBg,
            },
            statusBar: {
                visible: true,
                backgroundColor: theme.sidebarBg,
            },
            topBar: {
                visible: true,
                background: {
                    color: theme.sidebarBg,
                },
                backButton: {
                    visible: true,
                    color: theme.sidebarHeaderTextColor,
                },
            },
        }
            );
            return;
        }

        await dismissBottomSheet();
        showModal(Screens.CREATE_OR_EDIT_CHANNEL, title, {channelId, headerOnly: true},
               {
            layout: {
                componentBackgroundColor: theme.centerChannelBg,
            },
            statusBar: {
                visible: true,
                backgroundColor: theme.sidebarBg,
            },
            topBar: {
                visible: true,
                background: {
                    color: theme.sidebarBg,
                },
                backButton: {
                    visible: true,
                    color: theme.sidebarHeaderTextColor,
                },
            },}
        );
    }, [intl, channelId]);

    let text;
    if (isHeaderSet) {
        text = intl.formatMessage({id: 'channel_info.edit_header', defaultMessage: 'Edit Header'});
    } else {
        text = intl.formatMessage({id: 'channel_info.set_header', defaultMessage: 'Set Header'});
    }

    console.log(`\n\n\n\n\nthis the test id ${testID}\n\n\n\n\n`)
    return (
        <OptionBox
            isFromHome={isFromHome}
            containerStyle={containerStyle}
            iconName='pencil-outline'
            onPress={onSetHeader}
            testID={testID}
            text={text}
        />
    );
};

export default SetHeaderBox;
