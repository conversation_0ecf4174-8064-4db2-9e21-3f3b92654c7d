// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback } from 'react';
import { useIntl } from 'react-intl';

import CompassIcon from '@components/compass_icon';
import OptionBox from '@components/option_box';
import SlideUpPanelItem from '@components/slide_up_panel_item';
import { Screens } from '@constants';
import { useTheme } from '@context/theme';
import { dismissBottomSheet, showModal } from '@screens/navigation';

import { Dimensions, Pressable, Text, type StyleProp, type ViewStyle } from 'react-native';
import { InformationCircleIcon } from 'react-native-heroicons/outline'
import { changeOpacity } from '@app/utils/theme';

type Props = {
    channelId: string;
    containerStyle?: StyleProp<ViewStyle>;
    showAsLabel?: boolean;
    testID?: string;
    isDMorGM?: boolean | undefined

}

const InfoBox = ({ channelId, containerStyle, showAsLabel = false, testID,isDMorGM=false }: Props) => {
    const intl = useIntl();
    const theme = useTheme();

    const onViewInfo = useCallback(async () => {
        await dismissBottomSheet();
        const title = intl.formatMessage({ id: 'screens.channel_info', defaultMessage: 'Channel Info' });
        const closeButton = CompassIcon.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
        const closeButtonId = 'close-channel-info';

        const options = {
            topBar: {
                leftButtons: [{
                    id: closeButtonId,
                    icon: closeButton,
                    testID: 'close.channel_info.button',
                }],
            },
        };
        showModal(Screens.CHANNEL_INFO, title, { channelId, closeButtonId }, options);
    }, [intl, channelId, theme]);
    const windowWidth = Dimensions.get('window').width;
    if (showAsLabel) {
        return (
            <Pressable
                onPress={onViewInfo}
                style={{
                    display: 'flex'
                    , flexDirection: 'row',
                    alignItems: 'center',
                    borderRadius: 8,
                    paddingHorizontal:!isDMorGM?10:undefined,
                    borderWidth: 1, borderColor: changeOpacity(theme.sidebarText, 0.16),
                    height: !isDMorGM?58:38, width:!isDMorGM?windowWidth-40: (windowWidth / 2) - 25,
                     justifyContent: !isDMorGM?'space-between':'center',
                }}>
                <Text
                    style={{
                        fontFamily: 'IBMPlexSansArabic-Regular',
                        color: theme.sidebarText, marginEnd: 5
                    }}
                >
                    {intl.formatMessage({ id: 'channel_header.info', defaultMessage: 'View info' })}
                </Text>
                <InformationCircleIcon
                    color={changeOpacity(theme.sidebarText, 0.40)}
                    size={16}
                />
            </Pressable>
        );
    }

    return (
        <OptionBox
            containerStyle={containerStyle}
            iconName='information-outline'
            onPress={onViewInfo}
            testID={testID}
            text={intl.formatMessage({ id: 'intro.channel_info', defaultMessage: 'Info' })}
        />
    );
};

export default InfoBox;
