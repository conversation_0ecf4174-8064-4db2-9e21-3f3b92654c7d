// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useState } from 'react';
import { useIntl } from 'react-intl';

import { toggleMuteChannel } from '@actions/remote/channel';
import OptionBox from '@components/option_box';
import { useServerUrl } from '@context/server';
import { dismissBottomSheet } from '@screens/navigation';

import type { StyleProp, ViewStyle } from 'react-native';
import CompassIcon from '@app/components/compass_icon';
import Loading from '@app/components/loading';
import { useTheme } from '@app/context/theme';
import { changeOpacity, makeStyleSheetFromTheme } from '@app/utils/theme';

type Props = {
    channelId: string;
    containerStyle?: StyleProp<ViewStyle>;
    isMuted: boolean;
    showSnackBar?: boolean;
    testID?: string;
    isOnPostList?: boolean | null
    isFromHome?: boolean | null

}


const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        alignItems: 'center',
        backgroundColor: changeOpacity(theme.buttonBg, 0.08),
        borderRadius: 4,
        //flex: 1,
        // maxHeight: OPTIONS_HEIGHT,
        // justifyContent: 'center',
        minWidth: 50,
        paddingTop: 15,
        paddingBottom: 15,
        marginButton: 10,
        top: -10

    },

}));

const MutedBox = ({ channelId, containerStyle, isMuted, showSnackBar = false, testID, isOnPostList = false, isFromHome = false }: Props) => {
    const intl = useIntl();
    const serverUrl = useServerUrl();
    const [connecting, setConnecting] = useState(false);
    const theme = useTheme();
    const styles = getStyleSheet(theme);

    const handleOnPress = useCallback(async () => {
        setConnecting(true);

        await toggleMuteChannel(serverUrl, channelId, showSnackBar);
        setConnecting(false);

        await dismissBottomSheet();
    }, [channelId, isMuted, serverUrl, showSnackBar]);

    const muteActionTestId = isMuted ? `${testID}.unmute.action` : `${testID}.mute.action`;
    let iconNames = isMuted ? 'bell-off-outline' : 'bell-outline'


    if (connecting) {
        return (
            <Loading
                color={theme.buttonBg}
                size={'small'}
                footerText={""}
                containerStyle={styles.container}
                footerTextStyles={{}}
            />
        );
    }



    else if (isOnPostList) {
        return <CompassIcon

            name={iconNames}
            size={24}
        />
    }
    return (
        <OptionBox
            isFromHome={isFromHome}
            activeIconName='bell-off-outline'
            activeText={intl.formatMessage({ id: 'channel_info.muted', defaultMessage: 'Muted' })}
            containerStyle={containerStyle}
            iconName='bell-outline'
            isActive={isMuted}
            onPress={handleOnPress}
            testID={muteActionTestId}
            text={intl.formatMessage({ id: 'channel_info.muted', defaultMessage: 'Mute' })}
        />
    );
};

export default MutedBox;
