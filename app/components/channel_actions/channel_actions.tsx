// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useState } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import ChannelInfoStartButton from '@calls/components/channel_info_start';
import AddMembersBox from '@components/channel_actions/add_members_box';
import CopyChannelLinkBox from '@components/channel_actions/copy_channel_link_box';
import FavoriteBox from '@components/channel_actions/favorite_box';
import MutedBox from '@components/channel_actions/mute_box';
import SetHeaderBox from '@components/channel_actions/set_header_box';
import { useServerUrl } from '@context/server';
import { dismissBottomSheet, goToScreen } from '@screens/navigation';
import { isTypeDMorGM } from '@utils/channel';
import { Screens } from '@app/constants';
import uuid from 'react-native-uuid';
import { useIntl } from 'react-intl';
import OptionBox from '../option_box';
import { useTheme } from '@app/context/theme';
import type { UserModel } from '@app/database/models/server';
import useOtpController from '@app/controller/otpController';
import { fetchMe, type MyUserRequest } from '@actions/remote/user';

type Props = {
    channelId: string;
    channelType?: ChannelType;
    inModal?: boolean;
    dismissChannelInfo: () => void;
    callsEnabled: boolean;
    testID?: string;
    canManageMembers: boolean;
    isOnPostList?: boolean | null;
    isFromHome?: boolean | undefined,
    currentUser?: UserModel | undefined
}

export const CHANNEL_ACTIONS_OPTIONS_HEIGHT = 98;
const windowWidth = Dimensions.get('window').width;
const styles = StyleSheet.create({
    wrapper: {

        width: windowWidth - 45,
        flexDirection: 'row',
        justifyContent: 'space-between',
        //flexWrap:'wrap',
        height: CHANNEL_ACTIONS_OPTIONS_HEIGHT,
        // marginTop:15,
        alignItems: 'center'
    },
    separator: {
        width: 15,
    },
});

const ChannelActions = ({
    channelId,
    channelType,
    inModal = false,
    dismissChannelInfo,
    callsEnabled,
    canManageMembers,
    testID,
    isOnPostList = false,
    isFromHome = true
    , currentUser
}: Props) => {
    const serverUrl = useServerUrl();
    const intl = useIntl();

    const onCopyLinkAnimationEnd = useCallback(() => {
        if (!inModal) {
            requestAnimationFrame(async () => {
                await dismissBottomSheet();
            });
        }
    }, [inModal]);

    const isDM = isTypeDMorGM(channelType);




    const roomIDHOlder = uuid.v4();
    const room = roomIDHOlder?.toString();
    const startText = intl.formatMessage({ id: 'mobile.calls_start_vedio', defaultMessage: 'Start call' });
    const icon = 'video-outline';
    const theme = useTheme()
        const {getSavedPref}= useOtpController()
            // const [me,setMe] = useState<MyUserRequest|undefined>()



    const toggleJoinLeave = async () => {

        await dismissChannelInfo()
        let jitsiServerUrl = serverUrl;
        if (jitsiServerUrl.includes('https://')) {
            jitsiServerUrl = jitsiServerUrl.replace('https://', 'https://meet.')
        } else {
            jitsiServerUrl = jitsiServerUrl.replace('http://', 'https://meet.')
        }

        fetchMe(serverUrl).then((data)=>{
 goToScreen(Screens.WEB_VIEW_SCREAN, '', {
            roomHolder: room,
            channelId: channelId,
            roomUrl://savedJitsiUrl??
             jitsiServerUrl,
            serverUrl: serverUrl,
            isCreation: true,
            currentUser: data
            // currentUser: me
        }, {
            topBar: { visible: true },
            layout: {
                componentBackgroundColor: theme.centerChannelBg,
            },
            statusBar: {
                visible: true,
                backgroundColor: theme.sidebarBg,
            },
        },);

        }).catch((error)=>{
             goToScreen(Screens.WEB_VIEW_SCREAN, '', {
            roomHolder: room,
            channelId: channelId,
            roomUrl://savedJitsiUrl??
             jitsiServerUrl,
            serverUrl: serverUrl,
            isCreation: true,
            currentUser: undefined
            // currentUser: me
        }, {
            topBar: { visible: true },
            layout: {
                componentBackgroundColor: theme.centerChannelBg,
            },
            statusBar: {
                visible: true,
                backgroundColor: theme.sidebarBg,
            },
        },);
        })


       
    }

    return (
        <View style={styles.wrapper}>



            <MutedBox
                isFromHome={isFromHome}
                channelId={channelId}
                showSnackBar={!inModal}
                testID={testID}
                isOnPostList={isOnPostList}

            />
            <FavoriteBox
                isFromHome={isFromHome}
                channelId={channelId}
                showSnackBar={!inModal}
                testID={testID}
            />




            {isDM && !isOnPostList &&
                <SetHeaderBox
                    isFromHome={isFromHome}
                    channelId={channelId}
                    inModal={inModal}
                    testID={`${testID}.set_header.action`}
                />

            }
            {canManageMembers && !isOnPostList &&

                <AddMembersBox
                    channelId={channelId}
                    inModal={inModal}
                    testID={`${testID}.add_members.action`}
                    isFromHome={isFromHome}
                />

            }

            {/* {!isDM && !callsEnabled &&

                <>
                    <CopyChannelLinkBox
                        isFromHome={isFromHome}
                        channelId={channelId}
                        onAnimationEnd={onCopyLinkAnimationEnd}
                        testID={`${testID}.copy_channel_link.action`}

                    />
                </>
            } */}



            <>
                {/*
                <View style={styles.separator} />
                
                <ChannelInfoStartButtonVedio
                    serverUrl={serverUrl}
                    channelId={channelId}
                    dismissChannelInfo={dismissChannelInfo} 
                    isACallInCurrentChannel={false} 
                    alreadyInCall={false} 
                    limitRestrictedInfo={{
                        limitRestricted: false,
                        maxParticipants: 0,
                        isCloudStarter: false
                    }} otherParticipants={false} 
                    isAdmin={false} isHost={false} />*/}


                <OptionBox
                    isFromHome={isFromHome}
                    onPress={toggleJoinLeave}
                    text={startText}
                    iconName={icon}
                    activeText={startText}
                    activeIconName={icon}
                    isActive={false}
                    destructiveText={startText}
                    destructiveIconName={'video-outline'}
                    isDestructive={false}
                    testID='channel_info.channel_actions.join_start_call.action'
                />
                {

                    <>
                        <ChannelInfoStartButton
                            isFromHome={isFromHome}
                            serverUrl={serverUrl}
                            channelId={channelId}
                            dismissChannelInfo={dismissChannelInfo}
                        />
                    </>
                }

            </>
        </View>
    );
};

export default ChannelActions;
