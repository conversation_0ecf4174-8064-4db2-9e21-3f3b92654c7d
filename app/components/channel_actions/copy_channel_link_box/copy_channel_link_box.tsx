// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import Clipboard from '@react-native-clipboard/clipboard';
import React, { useCallback, useState } from 'react';
import { useIntl } from 'react-intl';

import AnimatedOptionBox from '@components/option_box/animated';
import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import OptionBox from '@app/components/option_box';
import Loading from '@app/components/loading';
import { changeOpacity, makeStyleSheetFromTheme } from '@app/utils/theme';
import { dismissBottomSheet } from '@app/screens/navigation';

type Props = {
    channelName?: string;
    onAnimationEnd?: () => void;
    teamName?: string;
    testID?: string;
    isFromHome?: boolean | null;

}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        alignItems: 'center',
        backgroundColor: changeOpacity(theme.buttonBg, 0.08),
        borderRadius: 15,
        //flex: 1,
        // maxHeight: OPTIONS_HEIGHT,
        // justifyContent: 'center',
        minWidth: 50,
        paddingTop: 15,
        paddingBottom: 15,
        marginButton: 10,
        top: -10

    },

}));
const CopyChannelLinkBox = ({ channelName, onAnimationEnd, teamName, testID, isFromHome = true }: Props) => {
    const intl = useIntl();
    const theme = useTheme();
    const serverUrl = useServerUrl();
    const [connecting, setConnecting] = useState(false);
    const styles = getStyleSheet(theme);

    const delay = (ms: number): Promise<void> => {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    const onCopyLink = useCallback(() => {
        setConnecting(true);

        Clipboard.setString(`${serverUrl}/${teamName}/channels/${channelName}`);

        delay(500).then(() => {
            setConnecting(false);
        })
   
        
    }, [channelName, teamName, serverUrl]);

    if (connecting) {
        return (
            <Loading
                color={theme.buttonBg}
                size={'small'}
                footerText={""}
                containerStyle={{
                    alignItems: 'center',
                    backgroundColor: changeOpacity(theme.buttonBg, 0.08),
                    borderRadius: isFromHome ? 15 : 5,
                    //flex: 1,
                    // maxHeight: OPTIONS_HEIGHT,
                    // justifyContent: 'center',
                    minWidth: 50,
                    paddingTop: 15,
                    paddingBottom: 15,

                    top: -10
                }}
                footerTextStyles={{}}
            />
        );
    }

    return (
        <OptionBox
            isFromHome={isFromHome}
            containerStyle={{}}
            iconName='link-variant'
            onPress={onCopyLink}
            testID={testID}
            text={intl.formatMessage({ id: 'channel_info.copy_link', defaultMessage: 'Copy Link' })}
        />
    );
};
{/* <AnimatedOptionBox
    animatedBackgroundColor={theme.onlineIndicator}
    animatedColor={theme.buttonColor}
    animatedIconName='check'
    animatedText={intl.formatMessage({id: 'channel_info.copied', defaultMessage: 'Copied'})}
    iconName='link-variant'
    onAnimationEnd={onAnimationEnd}
    onPress={onCopyLink}
    testID={testID}
    text={intl.formatMessage({id: 'channel_info.copy_link', defaultMessage: 'Copy Link'})}
/> */}

export default CopyChannelLinkBox;
