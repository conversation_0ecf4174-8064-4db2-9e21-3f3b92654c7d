import { StyleSheet, Text, View } from "react-native";
import React from "react";
const styles = StyleSheet.create({
    button: {
        paddingHorizontal: 16,
        paddingVertical: 2,
        borderRadius: 8,
        height: 34,
        borderColor: "#E1E4EA",
        borderWidth: 1,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
    },
    lable: {
        fontFamily: "IBMPlexSansArabic-SemiBold",
        color: "#001210",
        fontSize: 12,
    },
});
export default function ButtonBorder({ label }: { label: string }) {
    return (
        <View style={styles.button}>
            <Text style={styles.lable}>{label}</Text>
        </View>
    );
}
