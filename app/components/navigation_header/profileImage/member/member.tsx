// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { Keyboard, type StyleProp, StyleSheet, type ViewStyle } from 'react-native';

import ProfilePicture from '@components/profile_picture';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import { Screens } from '@constants';
import { openAsBottomSheet } from '@screens/navigation';

import type UserModel from '@typings/database/models/servers/user';

type Props = {
    channelId: string;
    containerStyle?: StyleProp<ViewStyle>;
    size?: number;
    showStatus?: boolean;
    theme: Theme;
    user: UserModel;
    getLastSeam: () => Promise<void>,
    chageUserState: (state: string) => void
}

const styles = StyleSheet.create({
    profile: {
        height: 67,
        marginBottom: 12,
        marginRight: 12,
    },
});



const Member = ({ channelId,
    containerStyle,
    size = 72,
    showStatus = true,
    theme, user,
    chageUserState,
    getLastSeam }: Props) => {
    //console.log(`\n\n\n\n\n\n\n\nthis the member ${user.status}    ${user}\n\n\n\n\n\n\n\n`)

    useEffect(() => {
        // if (user.status == 'offline')
        chageUserState(user.status)
        getLastSeam()

    }, [user.status])
    return (

        <ProfilePicture
            author={user}
            size={size}
            iconSize={48}
            showStatus={showStatus}
            statusSize={12}
            testID={`channel_intro.${user.id}.profile_picture`}
        />

    );
};

export default Member;
