// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useEffect, useMemo} from 'react';
import {defineMessages} from 'react-intl';
import {Text, View, type TextStyle} from 'react-native';

import {fetchProfilesInChannel} from '@actions/remote/user';
import FormattedText from '@components/formatted_text';
import {BotTag} from '@components/tag';
import {General, NotificationLevel} from '@constants';
import {useServerUrl} from '@context/server';
import {makeStyleSheetFromTheme} from '@utils/theme';
import {typography} from '@utils/typography';
import {getUserIdFromChannelName} from '@utils/user';


import Group from './group';
import Member from './member';

import type ChannelModel from '@typings/database/models/servers/channel';
import type ChannelMembershipModel from '@typings/database/models/servers/channel_membership';

type Props = {
    channel: ChannelModel;
    currentUserId: string;
    isBot: boolean;
    members?: ChannelMembershipModel[];
    theme: Theme;
    hasGMasDMFeature: boolean;
    channelNotifyProps?: Partial<ChannelNotifyProps>;
    userNotifyProps?: Partial<UserNotifyProps> | null;
    isOnHeader?:boolean|null;
    getLastSeam:() => Promise<void>,
    chageUserState:(state:string)=>void
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    botContainer: {
        alignSelf: 'flex-end',
        bottom: 7.5,
        height: 20,
        marginBottom: 0,
        marginLeft: 4,
        paddingVertical: 0,
    },
    botText: {
        fontSize: 14,
        lineHeight: 20,
    },
    container: {
        alignItems: 'center',
        marginHorizontal: 20,
    },
    message: {
        color: theme.centerChannelColor,
        marginTop: 8,
        textAlign: 'center',
        ...typography('Heading', 200, 'Light'),
    },
    boldText: {
        ...typography('Heading', 200, 'SemiBold'),
    },
    profilesContainer: {
        height:20,width:25,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        color: theme.centerChannelColor,
        marginTop: 4,
        textAlign: 'center',
        ...typography('Heading', 700, 'SemiBold'),
    },
    titleGroup: {
        ...typography('Heading', 600, 'SemiBold'),
    },
}));

const gmIntroMessages = defineMessages({
    muted: {id: 'intro.group_message.muted', defaultMessage: 'This group message is currently <b>muted</b>, so you will not be notified.'},
    [NotificationLevel.ALL]: {id: 'intro.group_message.all', defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'},
    [NotificationLevel.DEFAULT]: {id: 'intro.group_message.all', defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'},
    [NotificationLevel.MENTION]: {id: 'intro.group_message.mention', defaultMessage: 'You have selected to be notified <b>only when mentioned</b> in this group message.'},
    [NotificationLevel.NONE]: {id: 'intro.group_message.none', defaultMessage: 'You have selected to <b>never</b> be notified in this group message.'},
});

const getGMIntroMessageSpecificPart = (userNotifyProps: Partial<UserNotifyProps> | undefined | null, channelNotifyProps: Partial<ChannelNotifyProps> | undefined, boldStyle: TextStyle) => {
    const isMuted = channelNotifyProps?.mark_unread === 'mention';
    if (isMuted) {
        return (
            <FormattedText
                {...gmIntroMessages.muted}
                values={{
                    b: (chunk: string) => <Text style={boldStyle}>{chunk}</Text>,
                }}
            />
        );
    }
    const channelNotifyProp = channelNotifyProps?.push || NotificationLevel.DEFAULT;
    const userNotifyProp = userNotifyProps?.push || NotificationLevel.MENTION;
    let notifyLevelToUse = channelNotifyProp;
    if (notifyLevelToUse === NotificationLevel.DEFAULT) {
        notifyLevelToUse = userNotifyProp;
    }
    if (channelNotifyProp === NotificationLevel.DEFAULT && userNotifyProp === NotificationLevel.MENTION) {
        notifyLevelToUse = NotificationLevel.ALL;
    }

    return (
        <FormattedText
            {...gmIntroMessages[notifyLevelToUse]}
            values={{
                b: (chunk: string) => <Text style={boldStyle}>{chunk}</Text>,
            }}
        />
    );
};

const DirectChannel = ({
    channel,
    isBot,
    currentUserId,
    members,
    theme,
    hasGMasDMFeature,
    channelNotifyProps,
    userNotifyProps,isOnHeader=false,
    chageUserState,
    getLastSeam

}: Props) => {

    
     const profiles = useMemo(() => {
        if (channel.type === General.DM_CHANNEL) {
            const teammateId = getUserIdFromChannelName(currentUserId, channel.name);
            const teammate = members?.find((m) => m.userId === teammateId);
            
            if (!teammate) {
                return null;
            }
            return (
                <Member
                chageUserState={chageUserState}
                getLastSeam={getLastSeam}
                    channelId={channel.id}
                    containerStyle={{height: 20}}
                    member={teammate}
                    size={30}
                    theme={theme}
                />
            );
        }

       
    }, [members, theme]);

    return (
     
            <View   >
                {profiles}
            </View>
    

     
    );
};

export default DirectChannel;
