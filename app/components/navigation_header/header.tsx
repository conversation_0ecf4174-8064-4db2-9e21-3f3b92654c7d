// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Platform, Text, View } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import CompassIcon from '@components/compass_icon';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import ViewConstants from '@constants/view';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';
import type ChannelModel from '@typings/database/models/servers/channel';
import ProfileImage from './profileImage';
import { General } from '@app/constants';
import * as RNLocalize from 'react-native-localize';
import { DEFAULT_LOCALE } from '@app/i18n';
import { useServerUrl } from '@app/context/server';
import { fetchChannelCreator, fetchChannelMembersByIds, fetchChannelMemberships } from '@actions/remote/channel';
import user from '@app/database/schema/server/table_schemas/user';
import { UserModel } from '@app/database/models/server';
import FormattedTime from '../formatted_time';
import { getUserTimezone } from '@app/utils/user';
import { fetchStatusByIds, fetchStatusInBatch, fetchUsersByIds, fetchUsersByUsernames } from '@actions/remote/user';
import FormattedRelativeTime from '../formatted_relative_time';
import { useIntl } from 'react-intl';
import NetworkManager from '@managers/network_manager';
import FriendlyDate from '../friendly_date';
import usePostStore from '@app/controller/UserStateController';
import useUserStateController from '@app/controller/UserStateController';

export type HeaderRightButton = {
    borderless?: boolean;
    buttonType?: 'native' | 'opacity' | 'highlight';
    color?: string;
    iconName: string;
    onPress: () => void;
    rippleRadius?: number;
    testID?: string;
}

type Props = {
    defaultHeight: number;
    hasSearch: boolean;
    isLargeTitle: boolean;
    heightOffset: number;
    leftComponent?: React.ReactElement;
    onBackPress?: () => void;
    onTitlePress?: () => void;
    rightButtons?: HeaderRightButton[];
    scrollValue?: Animated.SharedValue<number>;
    lockValue?: Animated.SharedValue<number | null>;
    showBackButton?: boolean;
    subtitle?: string;
    subtitleCompanion?: React.ReactElement;
    theme: Theme;
    title?: string;
    channel?: ChannelModel | null;
    currentUser: UserModel | undefined


}

const hitSlop = { top: 20, bottom: 20, left: 20, right: 20 };
const rightButtonHitSlop = { top: 20, bottom: 5, left: 5, right: 5 };

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    centered: {
        marginEnd: 8,
        //alignItems: Platform.select({ android: 'flex-start', ios: 'center' }),
        display: 'flex',
        flexDirection: 'column',
        //marginStart:5
        // justifyContent:'flex-start',
        alignItems: 'flex-start',
        marginTop: 4
    },
    container: {
        alignItems: 'center',
        backgroundColor: theme.sidebarBg,
        // backgroundColor: 'green',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        paddingHorizontal: 16,
        zIndex: 10,


    },
    subtitleContainer: {
        flexDirection: 'row',
        //justifyContent: Platform.select({ android: 'flex-end', ios: 'center' }),
        justifyContent: 'flex-start',
        // left: Platform.select({ ios: undefined, default: 3 }),
    },
    subtitle: {
        color: changeOpacity(theme.sidebarHeaderTextColor, 0.72),
        ...typography('Heading', 25, 'Light'),
        lineHeight: 12,
        //marginBottom: 8,
        marginTop: 2,
        height: 13,
    },
    titleContainer: {
        // backgroundColor: 'red',

        alignItems: Platform.select({ android: 'flex-end', ios: 'center' }),
        justifyContent: 'center',
        flex: 3,
        height: '100%',
        paddingHorizontal: 8,
        ...Platform.select({
            ios: {
                paddingHorizontal: 60,
                flex: undefined,
                width: '100%',
                position: 'absolute',
                left: 16,
                bottom: 0,
                zIndex: 1,
            },
        }),
    },
    leftAction: {
        alignItems: 'center',
        flexDirection: 'row',
    },
    leftContainer: {
        height: '100%',
        justifyContent: 'center',
        ...Platform.select({
            ios: {
                paddingLeft: 16,
                zIndex: 5,
                position: 'absolute',
                bottom: 0,
            },
        }),
    },
    rightContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        height: '100%',
        justifyContent: 'flex-end',
        ...Platform.select({
            ios: {
                right: 16,
                bottom: 0,
                position: 'absolute',
                zIndex: 2,
            },
        }),
    },
    rightIcon: {
        marginLeft: 10,
    },
    title: {
        color: theme.sidebarHeaderTextColor,
        ...typography('Heading', 100,),
    },
}));

const Header = ({
    defaultHeight,
    hasSearch,
    isLargeTitle,
    heightOffset,
    leftComponent,
    onBackPress,
    onTitlePress,
    rightButtons,
    scrollValue,
    lockValue,
    showBackButton = true,
    subtitle,
    subtitleCompanion,
    theme,
    title,
    channel = null,
    currentUser = undefined

}: Props) => {

    const [localLange, setChangeLang] = useState<string>(`${DEFAULT_LOCALE}`);
    const serverUrl = useServerUrl()

    const { editState, editpostion } = useUserStateController()
    useEffect(() => {
        // Set the initial language based on device locale
        const locale = RNLocalize.getLocales()[0].languageCode;
        setChangeLang(locale);
        //i18n.changeLanguage(locale);
    }, []);

    const [channerLastSeen, changeLastSeent] = useState(0)
    const [userState, changeUserState] = useState<string | undefined>(undefined);


    function calculateDateDifference(date1: Date): string[] {
        const millisecondsDifference = Math.abs(date1.getTime() - Date.now());
        const secondsDifference = millisecondsDifference / 1000;
        const minutesDifference = Number(`${(secondsDifference / 60)}`.split('.')[0]) > (secondsDifference / 60 / 60) ? Number(`${(secondsDifference / 60)}`.split('.')[0]) % 60 : Number(`${(secondsDifference / 60)}`.split('.')[0]);
        // const minutesDifference = `${(secondsDifference / 60) / 60}`.split('.')[0];
        // const hoursDifference = `${(secondsDifference / 60 / 60)}`.split('.')[0];
        const hoursDifference = `${(secondsDifference / 60 / 60)}`.split('.')[0];





        // You can return the difference in any unit you prefer
        return [hoursDifference, minutesDifference.toString()]; // Returns the difference in days
    }

    async function getUserMember() {

        if (channel !== null && currentUser !== undefined) {
            try {

                const { statuses, error } = await fetchStatusByIds(serverUrl, [currentUser.id])
                if (error === undefined && statuses !== undefined) {
                    editpostion(currentUser.position)
                    changeLastSeent(statuses![statuses!.length - 1].last_activity_at)
                } else if (error !== undefined) {

                    changeLastSeent(0)
                }


            } catch (error) {
                console.log(`\n\n\n\n\n\n\nthis the error from getting channel members ${error}\n\n\n\n\n\n\n\n`)
            }
        }


    }




    useEffect(() => {
        getUserMember()
    }, [])


    useEffect(() => {
        editState(userState)
    }, [userState])

    const styles = getStyleSheet(theme);
    const insets = useSafeAreaInsets();

    const opacity = useAnimatedStyle(() => {
        if (!isLargeTitle) {
            return { opacity: 1 };
        }

        if (hasSearch) {
            return { opacity: 0 };
        }

        const barHeight = heightOffset - ViewConstants.LARGE_HEADER_TITLE_HEIGHT;
        const val = (scrollValue?.value ?? 0);
        const showDuration = 200;
        const hideDuration = 50;
        const duration = val >= barHeight ? showDuration : hideDuration;
        const opacityValue = val >= barHeight ? 1 : 0;
        return {
            opacity: withTiming(opacityValue, { duration }),
        };
    }, [heightOffset, isLargeTitle, hasSearch]);

    const containerAnimatedStyle = useAnimatedStyle(() => ({
        height: defaultHeight,
        paddingTop: insets.top,
    }), [defaultHeight, lockValue]);

    const containerStyle = useMemo(() => (
        [styles.container, containerAnimatedStyle]), [styles, containerAnimatedStyle]);

    const additionalTitleStyle = useMemo(() => ({
        marginLeft: Platform.select({ android: showBackButton && !leftComponent ? 20 : 0 }),
    }), [leftComponent, showBackButton, theme]);




    const element = useMemo(() => {
        switch (channel?.type) {
            case General.OPEN_CHANNEL:
            case General.PRIVATE_CHANNEL:
                break;
            default:
                return (
                    <ProfileImage
                        chageUserState={changeUserState}
                        getLastSeam={() => getUserMember()}
                        channel={channel!!}
                        theme={theme}
                    />
                );
        }
    }, [channel])



    const intl = useIntl();
    //  const lastSeenWord = intl.formatMessage({ id: 'channel_creator_last_seen', defaultMessage: 'last seen' })
    // const hour = intl.formatMessage({ id: 'Hour', defaultMessage: 'hour' })
    // const minits = intl.formatMessage({ id: 'minit', defaultMessage: 'minit' })
    //let joinWord = `${lastSeenWord}  ${Number(channerLastSeen[0]) > 0 ? `${Number(channerLastSeen[0])} ${hour}` : ''} ${Number(channerLastSeen[0]) > 0 && Number(channerLastSeen[1]) == 0 ? '' : `${Number(channerLastSeen[1])} ${minits}`}`

    const userStateHandler = (state: string | undefined) => {
        switch (state) {

            case 'away': {
                return intl.formatMessage({ id: 'status_dropdown.set_away', defaultMessage: 'inActive' })

            }

            case 'offline': {

                return intl.formatMessage({ id: 'user_status.offline', defaultMessage: 'offline' })
            }
            default: {
                return intl.formatMessage({ id: 'user_status.online', defaultMessage: 'active' })
            }
        }
    }


    const userStateColor = (state: string | undefined) => {
        switch (state) {

            case 'away': {
                return theme.awayIndicator

            }
            case 'offline': {
                return theme.dndIndicator

            }

            default: {
                return theme.onlineIndicator
            }
        }
    }


    return (
        <Animated.View
            style={[containerStyle]}
        >

            <Animated.View style={[additionalTitleStyle]}>
                <TouchableWithFeedback
                    disabled={!onTitlePress}
                    onPress={onTitlePress}
                    type='opacity'
                >
                    <View style={{
                        display: 'flex',
                        flexDirection: 'row-reverse',
                        alignItems: 'center',
                        justifyContent: 'center',

                    }}>


                        <View style={[styles.centered, { marginStart: 7 }]}>
                            {!hasSearch &&
                                <Animated.Text
                                    ellipsizeMode='tail'
                                    numberOfLines={1}
                                    style={[styles.title, opacity]}
                                    testID='navigation.header.title'
                                >
                                {title === "Town Square" ? "المجموعة الرئيسية" : title ===  "Off-Topic"  ? "المجموعة الفرعية" : title}
                                </Animated.Text>
                            }
                            {!isLargeTitle && Boolean(subtitle || subtitleCompanion) && channel?.type === 'D' &&
                                <View style={styles.subtitleContainer}>


                                    <Text
                                        style={{
                                            fontFamily: 'IBMPlexSansArabic-Regular',
                                            color: userStateColor(userState),
                                            marginEnd: 4,
                                            fontSize: 10,//marginStart:15
                                        }}
                                    >
                                        {userStateHandler(userState)}
                                    </Text>
                                    {(channerLastSeen !== 0 && userState !== 'online') &&<Text
                                        style={{
                                            fontFamily: 'IBMPlexSansArabic-Regular',
                                            color: userStateColor(userState),
                                            marginEnd: 4,
                                            fontSize: 10,//marginStart:15
                                        }}
                                    >
                                        {'اخر ظهور '}
                                    </Text>
                                        }
                                    {(channerLastSeen !== 0 && userState !== 'online') &&
                                        <FriendlyDate
                                            //sourceDate={channerLastSeen}
                                            value={new Date(channerLastSeen)}
                                            style={{
                                                color: changeOpacity(theme.centerChannelColor, 0.64),
                                                fontFamily: 'IBMPlexSansArabic-Regular',
                                                fontSize: 10
                                                //paddingStart:10
                                                // paddingStart:5
                                            }}
                                        />
                                    }

                                </View>
                            }
                        </View>
                        {channel !== null && element}
                        {showBackButton &&
                            <Animated.View style={[styles.leftContainer, { marginHorizontal: 0 }]}>
                                <TouchableWithFeedback
                                    borderlessRipple={true}
                                    onPress={onBackPress}
                                    rippleRadius={20}
                                    type={Platform.select({ android: 'native', default: 'opacity' })}
                                    testID='navigation.header.back'
                                    hitSlop={hitSlop}
                                >
                                    <Animated.View style={styles.leftAction}>
                                        <CompassIcon
                                            size={28}
                                            name={Platform.select({ android: 'chevron-right', ios: localLange === 'en' ? 'chevron-back-ios' : 'chevron-forward-ios' })!}
                                            color={theme.sidebarHeaderTextColor}
                                        />
                                        {
                                            //leftComponent
                                        }
                                    </Animated.View>
                                </TouchableWithFeedback>
                            </Animated.View>
                        }
                    </View>

                </TouchableWithFeedback>
            </Animated.View>


            {/*<Animated.View style={styles.rightContainer}>
                {Boolean(rightButtons?.length) &&
                    rightButtons?.map((r, i) => (
                        <TouchableWithFeedback
                            key={r.iconName}
                            borderlessRipple={r.borderless === undefined ? true : r.borderless}
                            hitSlop={rightButtonHitSlop}
                            onPress={r.onPress}
                            rippleRadius={r.rippleRadius || 20}
                            type={r.buttonType || Platform.select({ android: 'native', default: 'opacity' })}
                            style={i > 0 && styles.rightIcon}
                            testID={r.testID}
                        >
                            <CompassIcon
                                size={24}
                                name={r.iconName}
                                color={r.color || theme.sidebarHeaderTextColor}
                            />
                        </TouchableWithFeedback>
                    ))
                }
            </Animated.View>*/}
        </Animated.View>
    );
};

export default React.memo(Header);

