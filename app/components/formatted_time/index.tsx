import moment from 'moment';
import mtz from 'moment-timezone';
import React from 'react';
import { useIntl } from 'react-intl';
import { Text, View, type TextProps } from 'react-native';

import { getLocaleFromLanguage } from '@i18n';
import ArabicNumbers from '@app/utils/englishNumberToArabic';

type FormattedTimeProps = TextProps & {
    isMilitaryTime: boolean;
    timezone?: UserTimezone | string | undefined;
    value: number | string | Date;
};

const FormattedTime = ({ isMilitaryTime, timezone, value, ...props }: FormattedTimeProps) => {
    const { locale } = useIntl();
    moment.locale(getLocaleFromLanguage('en').toLowerCase());

    const getFormattedTime = () => {
        // Determine format based on military time preference
        const format = isMilitaryTime
            ? 'H:mm'
            : (mtz.localeData().longDateFormat('LT') || 'h:mm A');

        // Determine timezone or set default
        const zone = typeof timezone === 'object'
            ? (timezone.useAutomaticTimezone ? timezone.automaticTimezone : timezone.manualTimezone)
            : 'Asia/Aden';

        return timezone ? mtz.tz(value, zone).format(format) : mtz(value).format(format);
    };

    const formattedTime = getFormattedTime();
    const [time, period] = formattedTime.split(" ");
    const [hours, minutes] = time.split(':').map(Number);

    const periodInArabic = period === 'AM' ? 'ص' : 'م';
    const formattedArabicTime = `${ArabicNumbers(hours % 12 || 12)}:${ArabicNumbers(minutes)} ${periodInArabic}`;

    return (
        <Text {...props}>{formattedArabicTime}</Text>        
    );
};

export default FormattedTime;
