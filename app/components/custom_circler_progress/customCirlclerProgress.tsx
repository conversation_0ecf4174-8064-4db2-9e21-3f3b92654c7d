import React, { useEffect } from 'react';
import { View } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withRepeat, Easing } from 'react-native-reanimated';
import CircularProgress from 'react-native-circular-progress-indicator';

type CustomCircularProgressProps = {
  value: number;
  isCurrentUser?:boolean|undefined,
  isFromSearch?:boolean|undefined
};

const CustomCircularProgress = ({ value,isCurrentUser=false,isFromSearch=false }: CustomCircularProgressProps) => {
  
  // Shared value for rotation
  const rotation = useSharedValue(0);

  // Infinite rotation animation
  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, { duration: 3000, easing: Easing.linear }), 
      -1, 
      false 
    );
  }, []);

 
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  return (
    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
      <Animated.View style={animatedStyle}>
        <CircularProgress
          value={value*100}
          radius={isFromSearch?16:20}
          duration={2000}
          maxValue={100}
          showProgressValue={false}
          activeStrokeColor={isCurrentUser?"#009278":"white"}
          activeStrokeWidth={4}
          inActiveStrokeOpacity={0} // Hide background stroke
        />
      </Animated.View>
    </View>
  );
};

export default CustomCircularProgress;
