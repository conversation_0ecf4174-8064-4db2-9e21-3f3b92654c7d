import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Animated, Button, StyleSheet } from 'react-native';

const AnimatedList = (index) => {
  const [numbers] = useState(Array.from({ length: 100 }, (_, index) => index + 1));
  const [animations, setAnimations] = useState(numbers.map(() => new Animated.Value(0)));

  const moveToTop = (index) => {
    const updatedAnimations = [...animations];
    updatedAnimations[index] = new Animated.Value(1); // Move to top (start animation)
    setAnimations(updatedAnimations);

    // Perform animation for all numbers
    Animated.stagger(50, numbers.map((_, i) => 
      Animated.timing(animations[i], {
        toValue: 0, // To the top position
        duration: 500,
        useNativeDriver: true,
      })
    )).start();
  };

  const renderItem = ({ item, index }) => {
    return (
      <Animated.View 
        style={[
          styles.item,
          {
            transform: [{
              translateY: animations[index].interpolate({
                inputRange: [0, 1],
                outputRange: [300, 0], // Move from the middle (300) to the top (0)
              }),
            }],
          },
        ]}
      >
        <Text style={styles.text}>{item}</Text>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <Button title="Move 50th Number" onPress={() => moveToTop(49)} />
      <FlatList
        data={numbers}
        renderItem={renderItem}
        keyExtractor={(item) => item.toString()}
        extraData={animations}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  item: {
    padding: 10,
    marginVertical: 5,
    backgroundColor: 'skyblue',
    borderRadius: 5,
    width: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
    color: 'white',
  },
});

export default AnimatedList;