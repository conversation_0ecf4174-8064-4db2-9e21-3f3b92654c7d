// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/custom_status/clear_button should match snapshot 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "flex": 1,
      "height": 40,
      "justifyContent": "center",
      "opacity": 1,
      "width": 40,
    }
  }
  testID="clear_custom_status.button"
>
  <Icon
    name="close-circle"
    size={20}
    style={
      {
        "borderRadius": 1000,
        "color": "rgba(63,67,80,0.52)",
      }
    }
  />
</View>
`;
