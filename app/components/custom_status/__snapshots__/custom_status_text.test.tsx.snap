// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`components/custom_status/custom_status_text should match snapshot 1`] = `
<Text
  style={
    [
      {
        "color": "rgba(63,67,80,0.5)",
        "fontSize": 17,
        "includeFontPadding": false,
        "textAlignVertical": "center",
      },
      undefined,
    ]
  }
>
  In a meeting
</Text>
`;

exports[`components/custom_status/custom_status_text should match snapshot with empty text 1`] = `
<Text
  style={
    [
      {
        "color": "rgba(63,67,80,0.5)",
        "fontSize": 17,
        "includeFontPadding": false,
        "textAlignVertical": "center",
      },
      undefined,
    ]
  }
/>
`;
