// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

import Emoji from '@components/emoji';

import type {EmojiCommonStyle} from '@typings/components/emoji';
import {View, type StyleProp} from 'react-native';
import { useTheme } from '@app/context/theme';

interface ComponentProps {
    customStatus: UserCustomStatus;
    emojiSize?: number;
    style?: StyleProp<EmojiCommonStyle>;
    isStatePage?:boolean|undefined
}

const CustomStatusEmoji = ({customStatus,
     emojiSize = 20, style,
    isStatePage=false,}: ComponentProps) => {
    const theme = useTheme()
    if (customStatus.emoji) {
        return (
            <View
            style={!isStatePage&&{
                 borderRadius: 30,
                        borderWidth: 2,
                        borderColor: theme.buttonBg,
                      //  start: 10,
                        height: 50,
                        width: 50,
                       alignItems: 'center',
                        justifyContent: 'center',
                        marginStart:4
            }}>

            <Emoji
                size={20}
                emojiName={customStatus.emoji}
                commonStyle={style}
                />
                </View>
        );
    }

    return null;
};

export default CustomStatusEmoji;
