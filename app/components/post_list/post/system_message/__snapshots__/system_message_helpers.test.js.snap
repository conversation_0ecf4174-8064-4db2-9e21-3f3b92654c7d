// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`renderSystemMessage uses renderer for Channel Display Name update 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         updated the channel display name from: old displayname to: new displayname
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for Channel Header update 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         updated the channel header from: old header to: new header
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for Channel Purpose update 1`] = `
<Text
  style={
    {
      "color": "rgba(63,67,80,0.6)",
      "fontFamily": "IBMPlexSansArabic",
      "fontSize": 16,
      "fontWeight": "400",
      "lineHeight": 24,
    }
  }
>
  @username updated the channel purpose from: old purpose to: new purpose
</Text>
`;

exports[`renderSystemMessage uses renderer for Guest added and join to channel 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         joined the channel as a guest.
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for Guest added and join to channel 2`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @other.user
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         added to the channel as a guest by 
      </Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username.
        </Text>
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for OLD archived channel without a username 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
        archived the channel
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for archived channel 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         archived the channel
      </Text>
    </Text>
  </View>
</View>
`;

exports[`renderSystemMessage uses renderer for unarchived channel 1`] = `
<View
  style={
    {
      "marginBottom": 5,
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "flex-start",
          "flexDirection": "row",
          "flexWrap": "wrap",
        },
      ]
    }
    testID="markdown_paragraph"
  >
    <Text>
      <Text
        style={
          [
            {
              "color": "rgba(63,67,80,0.6)",
              "fontFamily": "IBMPlexSansArabic",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 24,
            },
            {
              "opacity": 1,
            },
          ]
        }
      >
        <Text
          style={[]}
        >
          @username
        </Text>
      </Text>
      <Text
        selectable={false}
        style={
          {
            "color": "rgba(63,67,80,0.6)",
            "fontFamily": "IBMPlexSansArabic",
            "fontSize": 16,
            "fontWeight": "400",
            "lineHeight": 24,
          }
        }
        testID="markdown_text"
      >
         unarchived the channel
      </Text>
    </Text>
  </View>
</View>
`;
