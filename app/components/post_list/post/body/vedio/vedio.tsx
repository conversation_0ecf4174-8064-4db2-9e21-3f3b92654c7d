// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Dimensions, type LayoutChangeEvent, Pressable, ScrollView, Text, useWindowDimensions, View } from 'react-native';
import Animated from 'react-native-reanimated';

import Markdown from '@components/markdown';
import { SEARCH, WEB_VIEW_SCREAN } from '@constants/screens';
import { useShowMoreAnimatedStyle } from '@hooks/show_more';
import { getMarkdownTextStyles, getMarkdownBlockStyles } from '@utils/markdown';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';


import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
import type { HighlightWithoutNotificationKey, SearchPattern, UserMentionKey } from '@typings/global/markdown';
import { goToScreen } from '@app/screens/navigation';
import { Screens } from '@app/constants';
import { useDatabase } from '@nozbe/watermelondb/react';
import { getUserById } from '@app/queries/servers/user';
import { useIntl } from 'react-intl';
import { VideoCameraIcon } from "react-native-heroicons/outline"
import MessageCornerSvg from '../message/message_corner_svg';
import { useServerUrl } from '@app/context/server';
import useOtpController from '@app/controller/otpController';
import { fetchMe, type MyUserRequest } from '@actions/remote/user';

type VedioProps = {
    currentUser?: UserModel;

    post: PostModel;
    theme: Theme;
    isCurrentUser?: boolean | null
    width: number;


}

const SHOW_MORE_HEIGHT = 54;

const EMPTY_MENTION_KEYS: UserMentionKey[] = [];
const EMPTY_HIGHLIGHT_KEYS: HighlightWithoutNotificationKey[] = [];

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        messageContainer: {
            // maxWidth: 250,
        },
        reply: {
            paddingRight: 10,
        },
        message: {
            //  color: theme.centerChannelColor,
            ...typography('Heading', 200),
            lineHeight: undefined, // remove line height, not needed and causes problems with md images
        },
        pendingPost: {
            opacity: 0.5,
        },
    };
});

const Vedio = ({ post, theme, isCurrentUser = false, width }: VedioProps) => {
    const [open, setOpen] = useState(false);
    const [height, setHeight] = useState<number | undefined>();
    const dimensions = useWindowDimensions();
    const maxHeight = Math.round((dimensions.height * 0.5) + SHOW_MORE_HEIGHT);
    const animatedStyle = useShowMoreAnimatedStyle(height, maxHeight, open);
    const style = getStyleSheet(theme);
    const blockStyles = getMarkdownBlockStyles(theme);
    const textStyles = getMarkdownTextStyles(theme);


    const windowWidth = Dimensions.get('window').width;

    const defaultWidth = windowWidth < 400 ? 290 : 306;



    const database = useDatabase()
    const [userData, changeUserData] = useState<UserModel | undefined>(undefined)
    const intl = useIntl();
    let roomLink = post?.props?.['meeting_id'] !== undefined ? post?.props?.['meeting_id'] : post?.message?.split('/')[3]

    const serverurl = useServerUrl()
    const { getSavedPref } = useOtpController()

    const [me, setMe] = useState<MyUserRequest | undefined>()

    useEffect(() => {

        getUserById(database, post.userId).then((data) => {
            changeUserData(data)
        })

        fetchMe(serverurl).then((data) => setMe(data))
    }, [])



    const toggleJoinLeave = () => {
        // const roomId =roomLink.split('/')[3].split('-')

        let jitsiServerUrl = serverurl;

        if (jitsiServerUrl.includes('https://')) {
            jitsiServerUrl = jitsiServerUrl.replace('https://', 'https://meet.')
        } else {
            jitsiServerUrl = jitsiServerUrl.replace('http://', 'https://meet.')
        }
        goToScreen(Screens.WEB_VIEW_SCREAN,
            ``
            , {
                roomHolder: roomLink,
                channelId: post.channelId,
                serverUrl: serverurl,
                roomUrl: jitsiServerUrl,
                isCreation: false,
                //currentUser: userData
                currentUser: me

            }, {
            topBar: { visible: true, },
            layout: {
                componentBackgroundColor: theme.centerChannelBg,
            },
            statusBar: {
                visible: true,
                backgroundColor: theme.sidebarBg,
            },
        });
    }


    return (
        <View style={{
            display: 'flex', flexDirection: isCurrentUser ? 'row' : 'row-reverse'
        }}>


            <View
                style={{

                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    height: 50,
                    borderColor: isCurrentUser ? "#00987e" : theme.sidebarTextHoverBg,
                    borderWidth: 5,
                    width: defaultWidth,
                    // paddingHorizontal: 10
                }}
            >


                <View style={{
                    marginStart: 4,
                    height: 40, width: 40, backgroundColor: isCurrentUser ? theme.centerChannelBg : theme.buttonBg,
                    borderRadius: 20,
                    alignItems: 'center',
                    justifyContent: 'center'

                }}>
                    <VideoCameraIcon
                        // name='video-outline'
                        size={24}
                        color={isCurrentUser ? theme.sidebarText : theme.centerChannelBg}
                    />
                </View>

                <View
                    style={{
                        display: 'flex',
                        //flexDirection:windowWidth>280?'column': 'row',
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>

                    <Text
                        numberOfLines={2}
                        style={{
                            textAlign: 'center',
                            width: defaultWidth - 145,
                            // backgroundColor:'red',
                            color: !isCurrentUser ? theme.sidebarText : "white",
                            ...typography('Heading', 75, 'SemiBold'),
                            paddingHorizontal: 7
                        }}
                    >
                        {` ${userData?.firstName ? userData?.firstName : ""}  ${intl.formatMessage({
                            id: 'custom_create_meeting',
                            defaultMessage: 'start meeting',
                        })}`}


                    </Text>
                </View>

                <Pressable
                    onPress={toggleJoinLeave}
                    style={{
                        width: 80,
                        height: 40,
                        backgroundColor: isCurrentUser ? theme.centerChannelBg : theme.buttonBg,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 7,
                        marginEnd: 5

                    }}
                >

                    <Text
                        style={{
                            ...typography('Heading', 100, 'SemiBold'), color: !isCurrentUser ? theme.centerChannelBg : theme.sidebarText
                        }}
                    >
                        {
                            intl.formatMessage({
                                id: 'mobile.calls_join',
                                defaultMessage: 'join meeting',
                            })
                        }
                    </Text>
                </Pressable>

            </View>
        </View>
    );
};

export default Vedio;
