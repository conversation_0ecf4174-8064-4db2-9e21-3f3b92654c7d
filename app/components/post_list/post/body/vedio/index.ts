// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {withDatabase, withObservables} from '@nozbe/watermelondb/react';

import {observeIfHighlightWithoutNotificationHasLicense} from '@queries/servers/system';
import {observeCurrentUser, observeCurrentUserById} from '@queries/servers/user';


import type {WithDatabaseArgs} from '@typings/database/database';
import Vedio from './vedio';

const withMessageInput = withObservables([], ({database}: WithDatabaseArgs) => {
    const currentUser = observeCurrentUserById(database,'me');
    
    return {
        currentUser,
   };
});

export default withDatabase(withMessageInput(Vedio));