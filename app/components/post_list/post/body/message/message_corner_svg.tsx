import React from 'react';
import Svg, { Path } from 'react-native-svg';

const MessageCornerSvg = ({ color, isCurrentUser = false }: { color: string, isCurrentUser: boolean }) => {
    if (!isCurrentUser)
        return (<Svg width={12} height={21} viewBox="0 0 15 21" fill="none">
            <Path
                d="M8 6.5V0.5H0.5V16.5C4.15938 19.7528 11.1252 20.3604 13.6 20.4739C13.9071 20.488 14.0646 20.1021 13.8479 19.8841C12.2002 18.227 8 13.3328 8 6.5Z"
                fill={color}
                transform="scale(-1, 1) translate(-15, 0)"
            />
        </Svg>)
    return (
        <Svg width={12} height={21} viewBox="0 0 15 21" fill="none">
            <Path d="M8 6.5V0.5H0.5V16.5C4.15938 19.7528 11.1252 20.3604 13.6 20.4739C13.9071 20.488 14.0646 20.1021 13.8479 19.8841C12.2002 18.227 8 13.3328 8 6.5Z" fill={color} />
        </Svg>
    );
};

export default MessageCornerSvg;