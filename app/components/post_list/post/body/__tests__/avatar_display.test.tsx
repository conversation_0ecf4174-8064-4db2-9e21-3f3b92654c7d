// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { render } from '@testing-library/react-native';

import { TestHelper } from '@test/test_helper';

import Body from '../index';

describe('Body Avatar Display Logic', () => {
    const baseProps = {
        appsEnabled: false,
        hasFiles: false,
        hasReactions: false,
        highlight: false,
        highlightReplyBar: false,
        isEphemeral: false,
        isJumboEmoji: false,
        isPendingOrFailed: false,
        isPostAddChannelMember: false,
        location: 'channel',
        searchPatterns: [],
        showAddReaction: true,
        theme: TestHelper.getTheme(),
        isCurrentUser: false,
        threadLength: 0,
    };

    const mockPost = {
        id: 'post-id',
        type: '',
        message: 'Test message',
        userId: 'user-id',
        channelId: 'channel-id',
        createAt: Date.now(),
        updateAt: Date.now(),
        deleteAt: 0,
        props: {},
        metadata: {},
    } as any;

    const mockAuthor = {
        id: 'user-id',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
    } as any;

    it('should not show avatar for regular text messages from other users', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: '',
                    message: 'Regular text message',
                }}
                author={mockAuthor}
                isCurrentUser={false}
            />
        );

        // Avatar should not be present for regular messages
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeNull();
    });

    it('should show avatar for voice messages from other users', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: 'custom_voice',
                    props: {
                        fileId: 'file-id',
                        duration: '30',
                    },
                }}
                author={mockAuthor}
                isCurrentUser={false}
            />
        );

        // Avatar should be present for voice messages
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeTruthy();
    });

    it('should show avatar for voice messages from current user', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: 'custom_voice',
                    props: {
                        fileId: 'file-id',
                        duration: '30',
                    },
                }}
                author={mockAuthor}
                isCurrentUser={true}
            />
        );

        // Avatar should be present for current user voice messages (always show for voice)
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeTruthy();
    });

    it('should show avatar for regular messages from current user when showProfileImage is true', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: '',
                    message: 'Current user message',
                }}
                author={mockAuthor}
                isCurrentUser={true}
                showProfileImage={true}
            />
        );

        // Avatar should be present for current user messages when showProfileImage is true
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeTruthy();
    });

    it('should not show avatar for regular messages from current user when showProfileImage is false', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: '',
                    message: 'Current user message',
                }}
                author={mockAuthor}
                isCurrentUser={true}
                showProfileImage={false}
            />
        );

        // Avatar should not be present for current user messages when showProfileImage is false
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeNull();
    });

    it('should not show avatar for image messages from other users', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: '',
                    message: '',
                }}
                author={mockAuthor}
                isCurrentUser={false}
                hasFiles={true}
            />
        );

        // Avatar should not be present for image/file messages
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeNull();
    });

    it('should not show avatar for jumbo emoji messages from other users', () => {
        const { queryByTestId } = render(
            <Body
                {...baseProps}
                post={{
                    ...mockPost,
                    type: '',
                    message: '😊',
                }}
                author={mockAuthor}
                isCurrentUser={false}
                isJumboEmoji={true}
            />
        );

        // Avatar should not be present for jumbo emoji messages
        expect(queryByTestId(`message_profile_header.${mockAuthor.id}.profile_picture`)).toBeNull();
    });
});
