// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useMemo, useRef, useState } from 'react';
import { FlatList, type ListRenderItemInfo, type StyleProp, type ViewStyle, View, Text, Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import NoResults from '@components/files_search/no_results';
import FormattedText from '@components/formatted_text';
import NoResultsWithTerm from '@components/no_results_with_term';
import { useTheme } from '@context/theme';
import { useIsTablet } from '@hooks/device';
import { useImageAttachments } from '@hooks/files';
import {
    getChannelNamesWithID,
    getFileInfosIndexes,
    getNumberFileMenuOptions,
    getOrderedFileInfos,
    getOrderedGalleryItems,
} from '@utils/files';
import { fileToGalleryItem, openGalleryAtIndex } from '@utils/gallery';
import { TabTypes } from '@utils/search';
import { preventDoubleTap } from '@utils/tap';
import { makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';

import { showMobileOptionsBottomSheet } from './file_options/mobile_options';
import Toasts from './file_options/toasts';
import FileResult from './file_result';

import type ChannelModel from '@typings/database/models/servers/channel';
import type { GalleryAction } from '@typings/screens/gallery';
import { useIntl } from 'react-intl';
import ArabicNumbers from '@app/utils/englishNumberToArabic';
import { useDerivedValue } from 'react-native-reanimated';
import useDownloadPrecentage from '@app/controller/downloadPrecentage';
import { AudioProvider } from '@app/context/AudioPlayController';

const getStyles = makeStyleSheetFromTheme((theme: Theme) => ({
    resultsNumber: {
        ...typography('Heading', 300),
        //padding: 50,
        paddingVertical: 20,
        paddingEnd: 15,
        color: theme.centerChannelColor,

    },
}));

type Props = {
    canDownloadFiles: boolean;
    fileChannels: ChannelModel[];
    fileInfos: FileInfo[];
    paddingTop: StyleProp<ViewStyle>;
    publicLinkEnabled: boolean;
    searchValue: string;
    isChannelFiles?: boolean;
    isFilterEnabled?: boolean;
}

const galleryIdentifier = 'search-files-location';

const separatorStyle = { height: 10 };
const Separator = () => <View style={separatorStyle} />;

const FileResults = ({
    canDownloadFiles,
    fileChannels,
    fileInfos,
    paddingTop,
    publicLinkEnabled,
    searchValue,
    isChannelFiles,
    isFilterEnabled,
}: Props) => {
    const intl = useIntl()
    const theme = useTheme();
    const styles = getStyles(theme);
    const insets = useSafeAreaInsets();
    const isTablet = useIsTablet();

    //const [action, setAction] = useState<GalleryAction>('none');
    const [action, setAction] = useState<GalleryAction>('downloading');
    const [lastViewedFileInfo, setLastViewedFileInfo] = useState<FileInfo | undefined>(undefined);

    const containerStyle = useMemo(() => ([paddingTop, { flexGrow: 1 }]), [paddingTop]);
    const numOptions = getNumberFileMenuOptions(canDownloadFiles, publicLinkEnabled);

    const { images: imageAttachments, nonImages: nonImageAttachments } = useImageAttachments(fileInfos, publicLinkEnabled);
    const filesForGallery = useMemo(() => imageAttachments.concat(nonImageAttachments), [imageAttachments, nonImageAttachments]);

    const channelNames = useMemo(() => getChannelNamesWithID(fileChannels), [fileChannels]);
    const orderedFileInfos = useMemo(() => getOrderedFileInfos(filesForGallery), [filesForGallery]);
    const fileInfosIndexes = useMemo(() => getFileInfosIndexes(orderedFileInfos), [orderedFileInfos]);
    const orderedGalleryItems = useMemo(() => getOrderedGalleryItems(orderedFileInfos), [orderedFileInfos]);
    const windowHeight = Dimensions.get('window').height;

    const onPreviewPress = useCallback(preventDoubleTap((idx: number) => {

        openGalleryAtIndex(galleryIdentifier, idx, [orderedGalleryItems[idx]]);
    }), [orderedGalleryItems]);

    const updateFileForGallery = (idx: number, file: FileInfo) => {
        'worklet';
        orderedFileInfos[idx] = file;
    };

    const onOptionsPress = useCallback((fInfo: FileInfo) => {
        setLastViewedFileInfo(fInfo);

        if (!isTablet) {
            showMobileOptionsBottomSheet({
                fileInfo: fInfo,
                insets,
                numOptions,
                setAction,
                theme,
            });
        }
    }, [insets, isTablet, numOptions, theme]);



    const [pregress, setProgress] = useState(0)

    const startDownloadRef = useRef<{ startDownload: (fInfo: FileInfo) => Promise<void>; } | undefined>(undefined);
    const cancelDownloadRef = useRef<{ cencleDownload: (fInfo?: FileInfo | undefined) => Promise<void>; } | undefined>(undefined)
    const handleDownload = async (fInfo: FileInfo) => {

        // await  cencelDownload();
        console.log('this from download start function');
        await startDownloadRef.current?.startDownload(fInfo)
    }

    const cencelDownload = async (fInfo?: FileInfo | undefined) => {
        console.log('this from cecle download call');
        await cancelDownloadRef.current?.cencleDownload(fInfo)
    }

    const renderItem = useCallback(({ item }: ListRenderItemInfo<FileInfo>) => {
        let channelName: string | undefined;
        if (!isChannelFiles && item.channel_id) {
            channelName = channelNames[item.channel_id];
        }
        const fileIfnoToGalleryItem = fileToGalleryItem(item);
 
        
        return (

            <FileResult
                canDownloadFiles={canDownloadFiles}
                channelName={channelName}
                fileInfo={item}
                index={fileInfosIndexes[item.id!] || 0}
                key={`${item.id}-${item.name}`}
                numOptions={numOptions}
                //onOptionsPress={onOptionsPress}
                onOptionsPress={handleDownload}
                clearDownload={cencelDownload}
                onPress={onPreviewPress}
                publicLinkEnabled={publicLinkEnabled}
                setAction={setAction}
                updateFileForGallery={updateFileForGallery}
                isFromSearch={true}
                isFromChannel={isChannelFiles}
                progressValue={pregress}
            />
        );
    }, [
        (orderedFileInfos.length === 1) && orderedFileInfos[0].mime_type,
        canDownloadFiles,
        channelNames,
        fileInfosIndexes,
        onPreviewPress,
        onOptionsPress,
        numOptions,
        publicLinkEnabled,
    ]);

    const noResults = useMemo(() => {
        if (!searchValue && isChannelFiles) {
            return (
                <View style={{ height: (windowHeight) - (windowHeight / 3) }}>
                    <NoResults isFilterEnabled={isFilterEnabled} />
                </View>
            );
        }
        return (
            <View style={{ height: (windowHeight) - (windowHeight / 3) }}>
                <NoResultsWithTerm
                    term={searchValue}
                    type={TabTypes.FILES}
                />
            </View>
        );
    }, [searchValue]);




    return (

        <View>

                {    //orderedFileInfos?.length<=0?
                    //<View></View>:
                    <FlatList
                        ListHeaderComponent={
                            <View
                                style={{
                                    marginStart: 20,
                                    flexDirection: 'row'
                                    , alignItems: 'center', gap: 5
                                }}>
                                <Text
                                    style={{
                                        marginVertical: 5,
                                        fontSize: 13,
                                        color: theme.sidebarText,
                                        fontFamily: "IBMPlexSansArabic-Bold",
                                    }}
                                >
                                    {ArabicNumbers(fileInfos.length)}
                                </Text>
                                <Text
                                    style={{
                                        fontSize: 13,
                                        color: theme.sidebarText,
                                        fontFamily: "IBMPlexSansArabic-Bold"
                                    }}
                                >
                                    {intl.formatMessage({ id: 'mobile.search.result' })}
                                </Text>

                            </View>
                        }
                        ItemSeparatorComponent={Separator}
                        ListEmptyComponent={noResults}
                        contentContainerStyle={containerStyle}
                        data={orderedFileInfos}
                        indicatorStyle='black'
                        initialNumToRender={10}

                        //@ts-expect-error key not defined in types
                        listKey={'files'}
                        maxToRenderPerBatch={5}
                        nestedScrollEnabled={true}
                        refreshing={false}
                        removeClippedSubviews={true}
                        renderItem={renderItem}
                        scrollEventThrottle={16}
                        scrollToOverflowEnabled={true}
                        showsVerticalScrollIndicator={true}
                        testID='search_results.post_list.flat_list'
                    />}


                <Toasts
                    action={action}
                    fileInfo={lastViewedFileInfo}
                    setAction={setAction}
                    isFromSearch={true}

                />
        </View>

    );
};

export default FileResults;
