// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useRef, useState } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import { useIsTablet } from '@hooks/device';
import { getViewPortWidth } from '@utils/images';

import TabletOptions from './file_options/tablet_options';

import type { GalleryAction } from '@typings/screens/gallery';
import { Audio } from 'expo-av';
import File from '../files/file';
import file from '@app/database/schema/server/table_schemas/file';
import { useDerivedValue } from 'react-native-reanimated';
import { useImageAttachments } from '@app/hooks/files';
import { preventDoubleTap } from '@app/utils/tap';
import { fileToGalleryItem, openGalleryAtIndex } from '@app/utils/gallery';
import Toasts from './file_options/toasts';
import useDownloadPrecentage from '@app/controller/downloadPrecentage';
import { AudioProvider } from '@app/context/AudioPlayController';

export type XyOffset = { x: number; y: number } | undefined;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        //marginHorizontal: 20,
    },
});

type Props = {
    canDownloadFiles: boolean;
    channelName?: string;
    fileInfo: FileInfo;
    index: number;
    numOptions: number;
    onOptionsPress: (finfo: FileInfo) => void;
    clearDownload?: () => Promise<void> | undefined;
    onPress: (idx: number) => void;
    publicLinkEnabled: boolean;
    isFromSearch?: boolean | null;
    isFromChannel?: boolean | undefined;
    setAction: (action: GalleryAction) => void;
    updateFileForGallery: (idx: number, file: FileInfo) => void;
    // setLastViewedFileInfo: ((fileInfo: FileInfo) => void | undefined) | undefined
    progressValue?: number | undefined;

}

//const galleryIdentifier = 'search-files-location';


const FileResult = ({
    canDownloadFiles,
    channelName,
    fileInfo,
    index,
    numOptions,
    onOptionsPress,
    onPress,
    publicLinkEnabled,
    //setAction,
    updateFileForGallery,
    isFromSearch = false,
    isFromChannel = undefined,
    clearDownload = undefined,
    progressValue = undefined
    // setLastViewedFileInfo=undefined
}: Props) => {


    const galleryIdentifier = `${fileInfo.id}-fileAttachments-${!isFromChannel ? 'Channel' : 'Thread'}`;


    const elementsRef = useRef<View | null>(null);
    const isTablet = useIsTablet();
    const isReplyPost = false;

    const [showOptions, setShowOptions] = useState<boolean>(false);
    const [openUp, setOpenUp] = useState<boolean>(false);
    const [xyOffset, setXYoffset] = useState<XyOffset>(undefined);
    const { height } = Dimensions.get('window');

    const handleOptionsPress = useCallback((fInfo: FileInfo) => {
        elementsRef.current?.measureInWindow((x, y) => {
            setOpenUp((y > height / 2));
            setXYoffset({ x, y });
            setShowOptions(true);
            onOptionsPress(fInfo);
        });
    }, []);




    const { images: imageAttachments, nonImages: nonImageAttachments } = useImageAttachments([fileInfo], publicLinkEnabled);

    const filesForGallery = useDerivedValue(() => imageAttachments.concat(nonImageAttachments),
        [imageAttachments, nonImageAttachments]);

    const attachmentIndex = (fileId: string) => {
        return filesForGallery.value.findIndex((file) => file.id === fileId) || 0;
    };

    const handlePreviewPress = preventDoubleTap((idx: number) => {
        const items = filesForGallery.value.map((f) => fileToGalleryItem(f, f.user_id,));

        console.log(`\n\n\nthis shown the item from result search file 
            ${JSON.stringify(items)}
            \n\n\n`)
        openGalleryAtIndex(galleryIdentifier, idx, items);
    });




    return (
            <>
                <View
                    ref={elementsRef}
                    style={styles.container}
                    collapsable={false}
                >

                    <File
                        isFromSearch={true}
                        asCard={true}
                        canDownloadFiles={canDownloadFiles}
                        channelName={channelName}
                        file={fileInfo}
                        galleryIdentifier={galleryIdentifier}
                        inViewPort={false}
                        index={attachmentIndex(fileInfo.id!)}
                        nonVisibleImagesCount={0}
                        ///onOptionsPress={handleOptionsPress}
                        onOptionsPress={onOptionsPress}
                        clearDownload={clearDownload}
                        onPress={handlePreviewPress}
                        optionSelected={isTablet && showOptions}
                        publicLinkEnabled={false}
                        showDate={true}
                        updateFileForGallery={updateFileForGallery}
                        wrapperWidth={(getViewPortWidth(isReplyPost, isTablet) - 6)}
                        isSingleImage={true}
                        progressValue={progressValue}
                    // setLastViewedFileInfo={setLastViewedFileInfo}
                    />
                </View>
                {/* {isTablet && showOptions && xyOffset &&
                <TabletOptions
                    fileInfo={fileInfo}
                    numOptions={numOptions}
                    openUp={openUp}
                    setAction={handleSetAction}
                    setShowOptions={setShowOptions}
                    xyOffset={xyOffset}
                />
            } */}


            </>
    );
};

export default FileResult;


