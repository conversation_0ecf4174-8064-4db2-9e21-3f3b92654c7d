// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`ErrorText should match snapshot 1`] = `
<Text
  style={
    [
      {
        "color": "#d24b4e",
        "fontSize": 12,
        "marginBottom": 15,
        "marginTop": 15,
        "textAlign": "left",
      },
      {
        "fontSize": 14,
        "marginHorizontal": 15,
      },
    ]
  }
  testID="error.text"
>
  Username must begin with a letter and contain between 3 and 22 characters including numbers, lowercase letters, and the symbols
</Text>
`;
