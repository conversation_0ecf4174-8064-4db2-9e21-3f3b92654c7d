// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import fontelloConfig from '@mattermost/compass-icons/config.json';
import {createIconSetFromFontello} from 'react-native-vector-icons';
import type {IconProps} from 'react-native-vector-icons/Icon';
import type {ImageRequireSource} from 'react-native';

const CompassIcon = createIconSetFromFontello(fontelloConfig, 'compass-icons',
    'compass-icons.ttf') as React.ComponentType<IconProps> & {
    getImageSource: (name: string, size?: number, color?: string) => Promise<ImageRequireSource>;
    getImageSourceSync: (name: string, size?: number, color?: string) => ImageRequireSource;
};

export default CompassIcon;
