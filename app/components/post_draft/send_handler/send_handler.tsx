// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { DeviceEventEmitter } from 'react-native';

import { updateDraftPriority } from '@actions/local/draft';
import { getChannelTimezones } from '@actions/remote/channel';
import { executeCommand, handleGotoLocation } from '@actions/remote/command';
import { createPost } from '@actions/remote/post';
import { handleReactionToLatestPost } from '@actions/remote/reactions';
import { setStatus } from '@actions/remote/user';
import { handleCallsSlashCommand } from '@calls/actions/calls';
import { Events, Screens } from '@constants';
import { PostPriorityType } from '@constants/post';
import { NOTIFY_ALL_MEMBERS } from '@constants/post_draft';
import { useServerUrl } from '@context/server';
import DraftUploadManager from '@managers/draft_upload_manager';
import * as DraftUtils from '@utils/draft';
import { isReactionMatch } from '@utils/emoji/helpers';
import { getFullErrorMessage } from '@utils/errors';
import { preventDoubleTap } from '@utils/tap';
import { confirmOutOfOfficeDisabled } from '@utils/user';
import { editPost as PostEditeHandler } from '@actions/remote/post';

import DraftInput from '../draft_input';

import type CustomEmojiModel from '@typings/database/models/servers/custom_emoji';
import type { SharedValue } from 'react-native-reanimated';
import usePostStore from '@app/controller/PostController';
import type { UserModel } from '@app/database/models/server';
import useUserStateController from '@app/controller/UserStateController';
import deleteImageFile from '@app/utils/deleteImgCash';

type Props = {

    testID?: string;
    channelId: string;
    channelType?: ChannelType;
    channelName?: string;
    rootId: string;
    canShowPostPriority?: boolean;
    setIsFocused: (isFocused: boolean) => void;

    // From database
    currentUser: UserModel;
    cursorPosition: number;
    enableConfirmNotificationsToChannel?: boolean;
    maxMessageLength: number;
    membersCount?: number;
    useChannelMentions: boolean;
    userIsOutOfOffice: boolean;
    customEmojis: CustomEmojiModel[];

    // DRAFT Handler
    value: string;
    files: FileInfo[];
    fileNotBelongToChannel: FileInfo[];
    clearDraft: () => void;
    updateValue: React.Dispatch<React.SetStateAction<string>>;
    updateCursorPosition: React.Dispatch<React.SetStateAction<number>>;
    updatePostInputTop: (top: number) => void;
    addFiles: (file: FileInfo[]) => void;
    uploadFileError: React.ReactNode;
    persistentNotificationInterval: number;
    persistentNotificationMaxRecipients: number;
    postPriority: PostPriority;
    groupCallsAllowed?: boolean | undefined;
    translationY?: SharedValue<number> | undefined;
    isVisibale?: SharedValue<boolean> | undefined;
    pageName:string

}

export const INITIAL_PRIORITY = {
    priority: PostPriorityType.STANDARD,
    requested_ack: true,
    persistent_notifications: false,
};

export default function SendHandler({

    testID,
    channelId,
    channelType,
    channelName,
    currentUser,
    enableConfirmNotificationsToChannel,
    files,
    maxMessageLength,
    membersCount = 0,
    cursorPosition,
    rootId,
    canShowPostPriority,
    useChannelMentions,
    userIsOutOfOffice,
    customEmojis,
    value,
    clearDraft,
    updateValue,
    addFiles,
    uploadFileError,
    updateCursorPosition,
    updatePostInputTop,
    setIsFocused,
    persistentNotificationInterval,
    persistentNotificationMaxRecipients,
    postPriority,
    groupCallsAllowed = false,
    translationY = undefined,
    isVisibale = undefined,
    pageName,
    fileNotBelongToChannel
}: Props) {
 
    const intl = useIntl();
    const serverUrl = useServerUrl();

    const [channelTimezoneCount, setChannelTimezoneCount] = useState(0);

    const [sendingMessage, setSendingMessage] = useState(false);

    const canSend = useCallback(() => {
        if (sendingMessage) {
            return false;
        }

        const messageLength = value.trim().length;

        if (messageLength > maxMessageLength) {
            return false;
        }

        if (files.length) {
            const loadingComplete = !files.some((file) => DraftUploadManager.isUploading(file.clientId!));
            return loadingComplete;
        }

        return messageLength > 0;
    }, [sendingMessage, value, files, maxMessageLength]);

    const handleReaction = useCallback((emoji: string, add: boolean) => {
        handleReactionToLatestPost(serverUrl, emoji, add, rootId);
        clearDraft();
        setSendingMessage(false);
    }, [serverUrl, rootId, clearDraft]);

    const handlePostPriority = useCallback((priority: PostPriority) => {
        updateDraftPriority(serverUrl, channelId, rootId, priority);
    }, [serverUrl, rootId]);

    const { editPost, postHolder } = usePostStore()
    const { userState, userPostion } = useUserStateController()
 
    const doSubmitMessage = useCallback(async () => {
        if (postHolder !== undefined && channelId === postHolder.channelId) {

            // Debug log removed to prevent React Native text rendering errors

            await PostEditeHandler(serverUrl, postHolder.id, value);
            editPost(undefined)
        }
        else {
            const postFiles = files.filter((f) => !f.failed);
            // Debug log removed to prevent React Native text rendering errors
            const post = {
                user_id: currentUser.id,
                channel_id: channelId,
                root_id: rootId,
                message: value,
                //message: `${userState === 'offline'&&(userPostion&&userPostion?.length>0?`سيتم عمل رنه للرقم  : ${userPostion}`:`المستخدم ليس لدية رقم هاتف !  : `)}\n`+value,
            } as Post;

            if (!rootId
                &&
                (
                    postPriority.priority ||
                    postPriority.requested_ack ||
                    postPriority.persistent_notifications)
            ) {
                post.metadata = {
                    priority: postPriority,
                };

            }

            if (rootId) {
                post.props = { 'ack': true };
            }

            // Debug log removed to prevent React Native text rendering errors
            createPost(serverUrl, post, postFiles)
            // .then(() => {
            //     postFiles.forEach((file) => {
            //         file?.localPath && deleteImageFile(file.localPath)
            //     })
            // });

        }




        clearDraft();

        setSendingMessage(false);
        DeviceEventEmitter.emit(Events.POST_LIST_SCROLL_TO_BOTTOM, rootId ? Screens.THREAD : Screens.CHANNEL);
        // Debug log removed to prevent React Native text rendering errors

    }, [files, currentUser.id, channelId, rootId, value, clearDraft, postPriority])





    const showSendToAllOrChannelOrHereAlert = useCallback((calculatedMembersCount: number, atHere: boolean) => {
        const notifyAllMessage = DraftUtils.buildChannelWideMentionMessage(intl, calculatedMembersCount, channelTimezoneCount, atHere);
        const cancel = () => {
            setSendingMessage(false);
        };

        DraftUtils.alertChannelWideMention(intl, notifyAllMessage, doSubmitMessage, cancel);
    }, [intl, channelTimezoneCount, doSubmitMessage]);


    const sendCommand = useCallback(async () => {
        if (value.trim().startsWith('/call')) {
            const { handled, error } = await handleCallsSlashCommand(value.trim(), serverUrl, channelId, channelType ?? '', rootId, currentUser.id, intl);
            if (handled) {
                setSendingMessage(false);
                clearDraft();
                return;
            }
            if (error) {
                setSendingMessage(false);
                DraftUtils.alertSlashCommandFailed(intl, error);
                return;
            }
        }

        const status = DraftUtils.getStatusFromSlashCommand(value);
        if (userIsOutOfOffice && status) {
            const updateStatus = (newStatus: string) => {
                setStatus(serverUrl, {
                    status: newStatus,
                    last_activity_at: Date.now(),
                    manual: true,
                    user_id: currentUser.id,
                });
            };
            confirmOutOfOfficeDisabled(intl, status, updateStatus);
            setSendingMessage(false);
            return;
        }

        const { data, error } = await executeCommand(serverUrl, intl, value, channelId, rootId);
        setSendingMessage(false);

        if (error) {
            const errorMessage = getFullErrorMessage(error);
            DraftUtils.alertSlashCommandFailed(intl, errorMessage);
            return;
        }

        clearDraft();

        if (data?.goto_location && !value.startsWith('/leave')) {
            handleGotoLocation(serverUrl, intl, data.goto_location);
        }
    }, [userIsOutOfOffice, currentUser.id, intl, value, serverUrl, channelId, rootId]);


    const sendMessage = useCallback(() => {
        const notificationsToChannel = enableConfirmNotificationsToChannel && useChannelMentions;
        const toAllOrChannel = DraftUtils.textContainsAtAllAtChannel(value);
        const toHere = DraftUtils.textContainsAtHere(value);

        if (value.indexOf('/') === 0) {
            sendCommand();
        } else if (notificationsToChannel && membersCount > NOTIFY_ALL_MEMBERS && (toAllOrChannel || toHere)) {
            showSendToAllOrChannelOrHereAlert(membersCount, toHere && !toAllOrChannel);
        } else {
            doSubmitMessage();
        }
    }, [
        enableConfirmNotificationsToChannel,
        useChannelMentions,
        value,
        channelTimezoneCount,
        sendCommand,
        showSendToAllOrChannelOrHereAlert,
        doSubmitMessage,
    ]);


    const handleSendMessage = useCallback(preventDoubleTap(() => {
        if (!canSend()) {
            return;
        }

        setSendingMessage(true);

        const match = isReactionMatch(value, customEmojis);
        if (match && !files.length) {
            handleReaction(match.emoji, match.add);
            return;
        }

        const hasFailedAttachments = files.some((f) => f.failed);
        if (hasFailedAttachments) {
            const cancel = () => {
                setSendingMessage(false);
            };
            const accept = () => {
                // Files are filtered on doSubmitMessage
                sendMessage();
            };

            DraftUtils.alertAttachmentFail(intl, accept, cancel);
        } else {
            sendMessage();
        }
    }), [canSend, value, handleReaction, files, sendMessage, customEmojis]);


    useEffect(() => {
        getChannelTimezones(serverUrl, channelId).then(({ channelTimezones }) => {
            setChannelTimezoneCount(channelTimezones?.length || 0);
        });
    }, [serverUrl, channelId]);


    return (
        <DraftInput
            currentUser={    currentUser}

           pageName={pageName}
            translationY={translationY}

            groupCallsAllowed={groupCallsAllowed}
            testID={testID}
            channelId={channelId}
            channelType={channelType}
            channelName={channelName}
            currentUserId={currentUser.id}
            rootId={rootId}
            canShowPostPriority={canShowPostPriority}
            cursorPosition={cursorPosition}
            updateCursorPosition={updateCursorPosition}
            value={value}
            files={files}
            updateValue={updateValue}
            addFiles={addFiles}
            uploadFileError={uploadFileError}
            sendMessage={handleSendMessage}
            canSend={canSend()}
            maxMessageLength={maxMessageLength}
            updatePostInputTop={updatePostInputTop}
            postPriority={postPriority}
            updatePostPriority={handlePostPriority}
            persistentNotificationInterval={persistentNotificationInterval}
            persistentNotificationMaxRecipients={persistentNotificationMaxRecipients}
            setIsFocused={setIsFocused}
            isVisibale={isVisibale}
            fileNotBelongToChannel={fileNotBelongToChannel}
        />
    );
}
