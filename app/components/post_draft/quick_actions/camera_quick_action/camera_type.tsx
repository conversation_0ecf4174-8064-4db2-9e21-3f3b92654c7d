// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { useIntl } from 'react-intl';
import { Text, TouchableHighlight, View } from 'react-native';

import FormattedText from '@components/formatted_text';
import SlideUpPanelItem from '@components/slide_up_panel_item';
import { useTheme } from '@context/theme';
import { useIsTablet } from '@hooks/device';
import { dismissBottomSheet } from '@screens/navigation';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';

import type { CameraOptions } from 'react-native-image-picker';
import { CameraIcon, VideoCameraIcon } from 'react-native-heroicons/outline';

type Props = {
    onPress: (options: CameraOptions) => void;
}
export const ITEM_HEIGHT = 48;

const getStyle = makeStyleSheetFromTheme((theme: Theme) => ({
    title: {
        color: theme.centerChannelColor,
        ...typography('Heading', 600, 'SemiBold'),
        marginBottom: 8,
    },
    container: {
        height: ITEM_HEIGHT,
        marginHorizontal: -20,
        paddingHorizontal: 20,
    },
    destructive: {
        color: theme.dndIndicator,
    },
    row: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
    },
    iconContainer: {
        height: ITEM_HEIGHT,
        justifyContent: 'center',
        marginRight: 10,
    },
    noIconContainer: {
        height: ITEM_HEIGHT,
        width: 18,
    },
    icon: {
        color: changeOpacity(theme.centerChannelColor, 0.56),
    },
    textContainer: {
        justifyContent: 'center',
        flex: 1,
        height: ITEM_HEIGHT,
        marginRight: 5,
    },
    text: {
        color: theme.centerChannelColor,
        ...typography('Heading', 200, 'Light'),
    },
}));

const CameraType = ({ onPress }: Props) => {
    const theme = useTheme();
    const isTablet = useIsTablet();
    const style = getStyle(theme);
    const intl = useIntl();

    const onPhoto = async () => {
        const options: CameraOptions = {
            quality: 0.8,
            mediaType: 'photo',
            saveToPhotos: true,
        };

        await dismissBottomSheet();
        onPress(options);
    };

    const onVideo = async () => {
        const options: CameraOptions = {
            videoQuality: 'high',
            mediaType: 'video',
            saveToPhotos: true,
        };

        await dismissBottomSheet();
        onPress(options);
    };

    return (
        <View>
            {!isTablet &&
                <FormattedText
                    id='mobile.camera_type.title'
                    defaultMessage='Camera options'
                    style={style.title}
                />
            }
            {/* <SlideUpPanelItem
                leftIcon='camera-outline'
                onPress={onPhoto}
                testID='camera_type.photo'
                text={intl.formatMessage({ id: 'camera_type.photo.option', defaultMessage: 'Capture Photo' })}
            />*/}


            {/*<SlideUpPanelItem
                leftIcon='video-outline'
                onPress={onVideo}
                testID='camera_type.video'
                text={intl.formatMessage({id: 'camera_type.video.option', defaultMessage: 'Record Video'})}
            />*/}
            <TouchableHighlight
                onPress={onPhoto}
                style={style.container}
                //testID={testID}
                underlayColor={changeOpacity(theme.buttonBg, 0.08)}
            >
                <View style={style.row}>
                     <CameraIcon
                        size={24}
                        color={changeOpacity(theme.centerChannelColor, 0.56)}
                        style={{
                            marginEnd: 9, marginStart: 5
                        }}
                    />
                   
                    <Text
                        style={{
                            fontFamily: "IBMPlexSansArabic-SemiBold",
                            /// fontSize:14,
                            color: theme.centerChannelColor
                            , ...typography('Heading', 200)
                        }}
                    >
                        {intl.formatMessage({ id: 'camera_type.photo.option', defaultMessage: 'Capture Photo' })}
                    </Text>
                  


                </View>
            </TouchableHighlight>


            <TouchableHighlight
                onPress={onVideo}
                style={style.container}
                //testID={testID}
                underlayColor={changeOpacity(theme.buttonBg, 0.08)}
            >
                <View style={style.row}>
                   <VideoCameraIcon
                        size={24}
                        color={changeOpacity(theme.centerChannelColor, 0.56)}
                        style={{
                            marginEnd: 9, marginStart: 5
                        }}
                    />
                   
                    <Text
                        style={{
                            fontFamily: "IBMPlexSansArabic-SemiBold",
                            /// fontSize:14,
                            color: theme.centerChannelColor
                            , ...typography('Heading', 200)
                        }}
                    >
                        {intl.formatMessage({ id: 'camera_type.video.option', defaultMessage: 'Record Video' })}
                    </Text>
                    


                </View>
            </TouchableHighlight>
        </View>
    );
};

export default CameraType;
