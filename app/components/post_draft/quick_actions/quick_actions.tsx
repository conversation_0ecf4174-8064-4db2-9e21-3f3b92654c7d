// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import CameraAction from './camera_quick_action';
import FileAction from './file_quick_action';
import ImageAction from './image_quick_action';
import InputAction from './input_quick_action';
import PostPriorityAction from './post_priority_action';
import { changeOpacity } from '@app/utils/theme';
import { useTheme } from '@app/context/theme';
import { EllipsisHorizontalCircleIcon } from 'react-native-heroicons/outline'
import { TouchableOpacity } from 'react-native-gesture-handler';
import LocationQuickAction from './location_quick_action';

const windowWidth = Dimensions.get('window').width;
type Props = {
    testID?: string;
    canUploadFiles: boolean;
    fileCount: number;
    isPostPriorityEnabled: boolean;
    canShowPostPriority?: boolean;
    maxFileCount: number;
    currentUserId: string;

    // Draft Handler
    value: string;
    channelID: string;
    channelTyp?: ChannelType | undefined;
    updateValue: (value: string) => void;
    addFiles: (file: FileInfo[]) => void;
    postPriority: PostPriority;
    updatePostPriority: (postPriority: PostPriority) => void;
    focus: () => void;
    onChannelQuickAction: () => void;
    groupCallsAllowed?: boolean | undefined
}

const style = StyleSheet.create({
    quickActionsContainer: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        width: "100%",
        height: 44,
        marginTop: 5,
    },
});

export default function QuickActions({
    testID,
    canUploadFiles,
    value,
    fileCount,
    currentUserId,
    isPostPriorityEnabled,
    canShowPostPriority,
    maxFileCount,
    updateValue,
    addFiles,
    postPriority,
    updatePostPriority,
    focus,
    channelID,
    channelTyp = undefined,
    groupCallsAllowed = false,
    onChannelQuickAction
}: Props) {

    const theme = useTheme()
    const atDisabled = value[value.length - 1] === '@';
    const slashDisabled = value.length > 0;

    const atInputActionTestID = `${testID}.at_input_action`;
    const locationActionTestID = `${testID}.location`;
    const slashInputActionTestID = `${testID}.slash_input_action`;
    const fileActionTestID = `${testID}.file_action`;
    const imageActionTestID = `${testID}.image_action`;
    const cameraActionTestID = `${testID}.camera_action`;
    const postPriorityActionTestID = `${testID}.post_priority_action`;

    const uploadProps = {
        disabled: !canUploadFiles,
        fileCount,
        maxFileCount,
        maxFilesReached: fileCount >= maxFileCount,
        onUploadFiles: addFiles,

    };




    return (
        <View
            testID={testID}
            style={style.quickActionsContainer}
        >
            {/* {
           // isPostPriorityEnabled && canShowPostPriority &&
             (
            )} */}
            <PostPriorityAction
                testID={postPriorityActionTestID}
                postPriority={postPriority}
                updatePostPriority={updatePostPriority}
            />
            <CameraAction
                testID={cameraActionTestID}
                {...uploadProps}
            />
            <ImageAction
                testID={imageActionTestID}
                {...uploadProps}
            />
            <FileAction
                testID={fileActionTestID}
                {...uploadProps}
            />

            <InputAction
                testID={slashInputActionTestID}
                disabled={slashDisabled}
                inputType='slash'
                updateValue={updateValue}
                focus={focus}
            />
            <InputAction
                testID={atInputActionTestID}
                disabled={atDisabled}
                inputType='at'
                updateValue={updateValue}
                focus={focus}
            />
            <LocationQuickAction
                testID={locationActionTestID}
                channelID={channelID}
                currentUserId={currentUserId}
            />
            <View style={{
                flexGrow: 1,
                alignItems: 'center',
                justifyContent: 'center',
            }}>
                <TouchableOpacity
                    onPress={onChannelQuickAction}
                    style={{
                        paddingTop: 0,
                    }}>
                    <EllipsisHorizontalCircleIcon
                        color={changeOpacity(theme.sidebarText, 0.65)}
                        size={25}

                    />
                </TouchableOpacity>
            </View>
        </View>
    );
}
