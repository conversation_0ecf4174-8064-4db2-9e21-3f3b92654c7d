// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useState } from 'react';

import CompassIcon from '@components/compass_icon';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import { ICON_SIZE } from '@constants/post_draft';
import { useTheme } from '@context/theme';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { ActivityIndicator, Alert, Platform, View } from 'react-native';
import * as Device from 'expo-device';
import * as Location from 'expo-location';
import { createPost } from '@actions/remote/post';
import { useServerUrl } from '@app/context/server';
import { useAlert } from '@app/context/alert';
type Props = {
    testID?: string;
    channelID?: string | undefined;
    currentUserId?: string | undefined;
}

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return {
        disabled: {
            tintColor: changeOpacity(theme.centerChannelColor, 0.16),
        },
        icon: {
            alignItems: 'center',
            justifyContent: 'center',
            flexGrow: 1,
        },
    };
});

const LocationQuickAction = ({
    testID,
    channelID,
    currentUserId
}: Props) => {
    const theme = useTheme();
    const [location, setLocation] = useState<Location.LocationObject | null>(null);
    const [errorMsg, setErrorMsg] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const serverUrl = useServerUrl();
    const style = getStyleSheet(theme);
    const { showAlert } = useAlert();


    ////////////////////////////// !! LOCATION  /////////////////////////////////////
    const fetchLocation = async () => {
        setLoading(true);
        if (Platform.OS === 'android' && !Device.isDevice) {
            showAlert('خطاء ما', "عذرًا، هذا لن يعمل على محاكي أندرويد. جرّب تشغيله على جهازك!");
            setLoading(false);
            return false;
        }

        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
            showAlert('خطأ', 'تم رفض الإذن للوصول إلى الموقع');
            setLoading(false);
            return false;
        }

        try {

            // Create a timeout promise that rejects in 10 seconds
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Timeout fetching location')), 10000)
            );

            // Race the location request against the timeout
            const loc = await Promise.race([
                Location.getCurrentPositionAsync({}),
                timeoutPromise
            ]);
            setLocation(loc);
            return true;
        } catch (error) {
            console.log(`\n\n\n\n this the error ${error} \n\n\n\n`)
            showAlert('خطأ', error.message === 'Timeout fetching location' ? 'انتهى الوقت أثناء جلب الموقع' : 'فشل في جلب الموقع');
        } finally {
            setLoading(false);
        }

        return false;
    };

    const getLocationLink = () => {
        if (location) {
            const { latitude, longitude } = location.coords;
            return `العنوان  https://www.google.com/maps?q=${latitude},${longitude}`;
        }
        return null;
    }; 

    const openLocationInBrowser = async () => {
        const isLocationAvailable = await fetchLocation();
        if (isLocationAvailable == false) return;
        const post = {
            user_id: currentUserId,
            channel_id: channelID,
            root_id: '',
            message: getLocationLink(),
        } as Post;

        const locationLink = getLocationLink();
        if (locationLink) {
            post.metadata = {
                priority: { "priority": "", "requested_ack": true, "persistent_notifications": false },
            };

            createPost(serverUrl, post, []);
        } else if (!loading && errorMsg) {
            showAlert('خطاء ما', errorMsg);
        }
    };
    ////////////////////////////// !! LOCATION  /////////////////////////////////////

    return (
        <TouchableWithFeedback
            disabled={loading}
            onPress={openLocationInBrowser}
            style={style.icon}
            type={'opacity'}
        >
            <View>
                {loading ? (
                    <ActivityIndicator size="small" color={changeOpacity(theme.centerChannelColor, 0.64)} />
                ) : (
                    <CompassIcon
                        name={'map-marker-outline'}
                        color={changeOpacity(theme.centerChannelColor, 0.64)}
                        size={ICON_SIZE}
                    />
                )}
            </View>
        </TouchableWithFeedback>
    );
};

export default LocationQuickAction;