// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import KeyboardTrackingView, { type KeyboardTrackingViewRef } from '@mattermost/keyboard-tracker';
import React, { type RefObject, useEffect, useState } from 'react';
import { Platform, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Autocomplete from '@components/autocomplete';
import { View as ViewConstants } from '@constants';
import { useServerUrl } from '@context/server';
import { useAutocompleteDefaultAnimatedValues } from '@hooks/autocomplete';
import { useIsTablet, useKeyboardHeight } from '@hooks/device';
import { useDefaultHeaderHeight } from '@hooks/header';

import Archived from './archived';
import DraftHandler from './draft_handler';
import ReadOnly from './read_only';
import type { SharedValue } from 'react-native-reanimated';
import usePostStore from '@app/controller/PostController';

const AUTOCOMPLETE_ADJUST = -5;
type Props = {

    testID?: string;
    accessoriesContainerID?: string;
    canPost: boolean;
    channelId: string;
    channelIsArchived?: boolean;
    channelIsReadOnly: boolean;
    deactivatedChannel: boolean;
    files?: FileInfo[];
    fileNotBelongToChannel?: FileInfo[];
    isSearch?: boolean;
    message?: string;
    rootId?: string;
    scrollViewNativeID?: string;
    keyboardTracker: RefObject<KeyboardTrackingViewRef>;
    containerHeight: number;
    isChannelScreen: boolean;
    canShowPostPriority?: boolean;
    groupCallsAllowed?: boolean | undefined;
    translationY?: SharedValue<number> | undefined;
    isVisibale?: SharedValue<boolean> | undefined;
    pageName: string

}

const { KEYBOARD_TRACKING_OFFSET } = ViewConstants;

function PostDraft({
    testID,
    accessoriesContainerID,
    canPost,
    channelId,
    channelIsArchived,
    channelIsReadOnly,
    deactivatedChannel,
    files,
    isSearch,
    message = '',
    rootId = '',
    scrollViewNativeID,
    keyboardTracker,
    containerHeight,
    isChannelScreen,
    canShowPostPriority,
    groupCallsAllowed = false,
    translationY = undefined,
    isVisibale = undefined,
    pageName,
    fileNotBelongToChannel
}: Props) {


    const [value, setValue] = useState(message);
    const [cursorPosition, setCursorPosition] = useState(message.length);
    const [postInputTop, setPostInputTop] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const isTablet = useIsTablet();
    const keyboardHeight = useKeyboardHeight(keyboardTracker);
    const insets = useSafeAreaInsets();
    const headerHeight = useDefaultHeaderHeight();
    const serverUrl = useServerUrl();

    // Update draft in case we switch channels or threads
    useEffect(() => {
        setValue(message);
        setCursorPosition(message.length);
    }, [channelId, rootId]);


    const { postHolder } = usePostStore()

    useEffect(() => {
        if (postHolder !== undefined && postHolder.message) {
            setValue(postHolder?.message ?? '');
            setCursorPosition(postHolder?.message.length);
        }
    }, [postHolder])
    
    

    const keyboardAdjustment = (isTablet && isChannelScreen) ? KEYBOARD_TRACKING_OFFSET : 0;
    const insetsAdjustment = (isTablet && isChannelScreen) ? 0 : insets.bottom;
    const autocompletePosition = AUTOCOMPLETE_ADJUST + Platform.select({
        ios: (keyboardHeight ? keyboardHeight - keyboardAdjustment : (postInputTop + insetsAdjustment)),
        default: postInputTop + insetsAdjustment,
    });
    const autocompleteAvailableSpace = containerHeight - autocompletePosition - (isChannelScreen ? headerHeight : 0);

    const [animatedAutocompletePosition, animatedAutocompleteAvailableSpace] = useAutocompleteDefaultAnimatedValues(autocompletePosition, autocompleteAvailableSpace);




    if (channelIsArchived || deactivatedChannel) {
        const archivedTestID = `${testID}.archived`;

        return (

            <Archived
                testID={archivedTestID}
                deactivated={deactivatedChannel}
            />

        );
    }

    if (channelIsReadOnly || !canPost) {
        const readOnlyTestID = `${testID}.read_only`;

        return (

            <ReadOnly
                testID={readOnlyTestID}
            />

        );
    }



    const draftHandler = (
        <DraftHandler
            pageName={pageName}
            translationY={translationY}
            groupCallsAllowed={groupCallsAllowed}
            testID={testID}
            channelId={channelId}
            cursorPosition={cursorPosition}
            files={files}
            rootId={rootId}
            canShowPostPriority={canShowPostPriority}
            updateCursorPosition={setCursorPosition}
            updatePostInputTop={setPostInputTop}
            updateValue={setValue}
            value={value}
            setIsFocused={setIsFocused}
            isVisibale={isVisibale}
            fileNotBelongToChannel={fileNotBelongToChannel}
        />

    );

    const autoComplete = isFocused ? (
        <View style={{ bottom: 16.9 }}>

            <Autocomplete
                position={animatedAutocompletePosition}
                updateValue={setValue}
                rootId={rootId}
                channelId={channelId}
                cursorPosition={cursorPosition}
                value={value}
                isSearch={isSearch}
                hasFilesAttached={Boolean(files?.length)}
                inPost={true}
                availableSpace={animatedAutocompleteAvailableSpace}
                serverUrl={serverUrl}
            />
        </View>

    ) : null;

    if (Platform.OS === 'android') {
        return (
            <>
                {draftHandler}
                {autoComplete}

            </>
        );
    }

    return (
        <>


            <KeyboardTrackingView
                accessoriesContainerID={accessoriesContainerID}
                ref={keyboardTracker}
                scrollViewNativeID={scrollViewNativeID}
                viewInitialOffsetY={isTablet && !rootId ? KEYBOARD_TRACKING_OFFSET : 0}
            >
                {draftHandler}
            </KeyboardTrackingView>
            {autoComplete}


        </>
    );
}

export default PostDraft;
