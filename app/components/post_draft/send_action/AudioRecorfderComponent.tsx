

import { changeOpacity, makeStyleSheetFromTheme } from '@app/utils/theme';
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Image, Pressable, PanResponder, TouchableWithoutFeedback, Platform, Dimensions, TouchableOpacity, type PanResponderGestureState, useAnimatedValue } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { useTheme } from '@context/theme';
import type { Recording } from 'expo-av/build/Audio';
import { Audio } from 'expo-av';
import FilePickerUtil from '@app/utils/file/file_picker';
import { useIntl } from 'react-intl';
import type { DocumentPickerResponse } from 'react-native-document-picker';
import CompassIcon from '@app/components/compass_icon';
import { useServerUrl } from '@app/context/server';


import * as RNLocalize from 'react-native-localize';

import { Gesture, GestureDetector, GestureHandlerRootView, PanGestureHandler, type GestureEvent, type PanGestureHandlerEventPayload } from 'react-native-gesture-handler';
import { typography } from '@app/utils/typography';
import { DEFAULT_LOCALE } from '@app/i18n';
import ArabicNumbers from '@app/utils/englishNumberToArabic';
import Animated, { useSharedValue, useAnimatedStyle, type SharedValue } from 'react-native-reanimated';
import { runOnJS } from 'react-native-reanimated';
import { PaperAirplaneIcon, TrashIcon, MicrophoneIcon } from 'react-native-heroicons/outline'
import LottieView from 'lottie-react-native';
import useLockPositionController from '@app/controller/LockPositionControoler';
import { props } from 'lodash/fp';
import useAudioRecordController from '@app/controller/AudioRecordController';
import { AudioPlayController } from '@app/context/AudioPlayController';


const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
  return {
    disableButton: {
      backgroundColor: changeOpacity(theme.buttonBg, 0.3),
    },
    sendButtonContainer: {
      //justifyContent: 'flex-end',
      // paddingRight: 8,
      flex: 1
      ,
    },
    recordIconStyle: {
      height: 40, width: 40
      ,
      marginTop: 5,
      marginStart: 3,
      alignItems: 'center',
      backgroundColor: changeOpacity(theme.buttonBg, 0.3),
      borderWidth: 1,
      borderColor: theme.buttonBg,
      borderRadius: 25,
      dispaly: 'flex',
      justifyContent: 'center',

    },

    basicIconStyle: {
      height: 50, width: 50, alignItems: 'center',
      //backgroundColor: changeOpacity(theme.buttonBg, 0.3),

      dispaly: 'flex',
      justifyContent: 'center',
      marginTop: 5,
      marginStart: 3

    },

    iconStyle: {
      height: 25, width: 25,
      resizeMode: 'contain',
      // marginStart:1

    },
    sendButton: {
      backgroundColor: theme.buttonBg,
      borderRadius: 4,
      height: 32,
      width: 80,
      alignItems: 'center',
      justifyContent: 'center',
    },
  };
});

type Props = {

  translationYss?: SharedValue<number> | undefined;
  isVisibale?: SharedValue<boolean> | undefined;

  sendingFile: (files: FileInfo[]) => void;
  currentUserID: string;
  rootID: string;
  channelId: string;

  changePressingState: (state: boolean) => void;
  isPressing: boolean,
  isFromThread?: boolean | false;
  pageName: string

}


export default function AudioRecorfderComponent({
  translationYss,
  //  isVisibale = undefined,
  sendingFile,
  currentUserID,
  rootID,
  channelId,
  changePressingState
  , isPressing,
  isFromThread = true,
  pageName

}: Props) {

  const [localLange, setChangeLang] = useState<string>(`${DEFAULT_LOCALE}`);

  useEffect(() => {
    const locale = RNLocalize.getLocales()[0].languageCode;
    setChangeLang(locale);
  }, []);
  const translationX = useSharedValue(0);



  const animatedStyle = useAnimatedStyle(() => {

    return {

      transform: [
        { translateX: translationX.value }

      ]

    };

  });


  const isSwappingUpListener = useSharedValue(false)

  const theme = useTheme();
  const serverUrl = useServerUrl()
  const styles = getStyleSheet(theme);
  const intl = useIntl();
  const [permissionResponse, requestPermission] = Audio.usePermissions();
  const cencleDirection = intl.formatMessage({ id: 'dragg_right_to_cencle', defaultMessage: 'cencleDirection' })
  const { changeVisibility } = useLockPositionController()
  const [numberHolder, setNumberHolder] = useState(0)
  const {
     startRecording,
    recording,
    minits,
    second,
    changeDuration,
    stopRecording,
    isSwappingUp,
    isSwappingRightOrLeft,
    changeSwappingUpState,
    changeSwappingRigntOrLeftState } = useAudioRecordController()
  const { puseAudio } = AudioPlayController()

  useEffect(() => {

    if (recording) {
      const intervalId = setInterval(() => {
        changeDuration(
          () => changePressingState(false),
          uploadFileHndler, 
        );
      }, 1000);

      return () => {
        setNumberHolder(numberHolder + 1);
        clearInterval(intervalId)
      };
    }
      return undefined;
  }, [recording && pageName === 'thread']);




  const complateRecordingState = () => {
    changeSwappingUpState(false)
    changePressingState(false)
    isSwappingUpListener.value = false
  }
  const deleteImageFile = async () => {
    complateRecordingState()

    await stopRecording().then((data) => {

      FileSystem.getInfoAsync(data)
        .then((result) => {
          data.length > 0 && FileSystem.deleteAsync(result.uri).catch((error) => {
            // Error handling - log removed to prevent React Native text rendering errors

          })
            .then(() => {
            })
        });
    })
    return;
  }

  const uploadFileHndler = async () => {

    complateRecordingState()
    // const duration = await getDurationAudio()
    stopRecording().then((data) => {
      FileSystem.getInfoAsync(data)//.exists(filepath)

        .then(async (result) => {
          var fileNameToList = data.split('/');
          var file = new FilePickerUtil(intl, sendingFile);

          var audioPicker = {
            uri: data,
            name: fileNameToList[fileNameToList.length - 2],
            copyError: null,
            fileCopyUri: null,
            type: fileNameToList[fileNameToList.length - 1].split('.')[1],
            size: undefined
          } as unknown as DocumentPickerResponse;

          file.prepareFileUploadAudio(audioPicker)


        });
    })
  }

  const longPress = Gesture.LongPress()
    .minDuration(1)
    .onStart(() => {
      if (translationYss !== undefined) translationYss.value = -240

      runOnJS(changePressingState)(true)
      runOnJS(changeVisibility)(true)
      runOnJS(changeSwappingRigntOrLeftState)(false)

      if (!recording)
        runOnJS(startRecording)(permissionResponse, requestPermission, puseAudio)
    })

  const pandGesture = Gesture.Pan()
    //.enabled(!isSwappingUpListener)
    .onUpdate((event) => {

      if ((isSwappingUp || isSwappingUpListener.value)) {
        // Debug log removed to prevent React Native text rendering errors

        return
      } else {
        if (event.translationY < -52) {
          if (translationYss !== undefined) translationYss.value = 0
          isSwappingUpListener.value = true
          runOnJS(changeSwappingUpState)(true)
          runOnJS(changeVisibility)(false)
          // Debug log removed to prevent React Native text rendering errors

        } else if (event.translationY < 0 || event.translationY < -50) {
          if (translationYss !== undefined) {
            translationYss.value = event.translationY - 240;
            // runOnJS(changeYPostion)(translationYss.value)
          }
        }
        if ((event.translationX > -30)) {
          translationX.value = event.translationX;
        }
        else if (event.translationX < -30 || event.translationX < 30) {
          runOnJS(changeSwappingRigntOrLeftState)(true)
          runOnJS(changePressingState)(false)
          runOnJS(changeVisibility)(false)
          if (translationYss !== undefined) translationYss.value = 0
          runOnJS(deleteImageFile)()

          return;

        }

      }

    })
    .onFinalize(() => {
      if (translationYss !== undefined) translationYss.value = 0
      if (!isSwappingUp) {
        runOnJS(changePressingState)(false)
        runOnJS(changeVisibility)(true)
        runOnJS(uploadFileHndler)()
      }
      translationX.value = 0;
    })

    
  const concateGesture = Gesture.Simultaneous(longPress, pandGesture)
  const windowWidth = Dimensions.get('window').width;
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <View style={{
          display: 'flex'
          ,
          paddingEnd: isPressing ? 50 : undefined,
          flexDirection: 'row-reverse',
          alignItems: 'center',
          justifyContent: 'space-between',
          ///   backgroundColor: 'green',
          width:
            isPressing ?
              windowWidth
              : undefined
        }}>

          {isPressing &&
            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingTop: 8 }}>

              <Text style={{ ...typography('Body', 100, 'Regular'), marginStart: 5, color: theme.sidebarText, marginEnd: 5 }}>
                {`${minits >= 10 ? '' : `${ArabicNumbers(0)}`}${ArabicNumbers(minits)}:${second >= 10 ? '' : `${ArabicNumbers(0)}`}${ArabicNumbers(second)}`}
              </Text>
              <View style={{ height: 10, width: 10, borderRadius: 9, backgroundColor: 'red', marginTop: 2 }}></View>
            </View>}
          <GestureDetector
            gesture={concateGesture}
          >
            <View style={{
              display: 'flex', flexDirection: 'row', alignItems: 'center'
            }} >
              {(!isSwappingUp) && <View style={[isPressing ? styles.recordIconStyle : styles.basicIconStyle]}>

                <MicrophoneIcon style={[
                  styles.iconStyle,
                ]} color={theme.buttonBg} />


              </View>}

              {(isPressing && !isSwappingUp) && <Animated.View style={[{
                width: 180, height: 50,
                zIndex: 100,

              }, animatedStyle]}

              >
                <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginStart: 15, justifyContent: 'center', marginTop: 16 }}>

                  <CompassIcon
                    name={Platform.select({ android: 'chevron-right', ios: localLange === 'en' ? 'chevron-back-ios' : 'chevron-forward-ios' })!}
                    size={24}
                    color={changeOpacity(theme.sidebarText, 0.40)}
                  />
                  <Text
                    style={{
                      ...typography('Heading', 200, 'Light'), marginStart: 4, marginEnd: 5,

                      color: changeOpacity(theme.sidebarText, 0.40)
                    }}>
                    {cencleDirection}
                  </Text>
                </View>
              </Animated.View>
              }
              {isSwappingUp &&
                <View style={{
                  height: 15,
                  overflow: 'hidden',
                  // width:windowWidth
                  marginTop: 5
                }}>
                  <LottieView
                    resizeMode='cover'

                    style={{
                      //position:'absolute', 
                      //,top:1,
                      bottom: 15,
                      //flex:1,
                      height: 2,
                      width: windowWidth,
                      marginStart: -3, marginVertical: 5
                    }}
                    source={require("assets/base/images/audioWaveAnimation.json")}
                    autoPlay
                    loop
                    colorFilters={[
                      {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 36', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      },
                      {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 35', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 34', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 33', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 32', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 31', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 30', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 29', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 28', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 27', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 26', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 25', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 24', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 23', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 22', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 20', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 19', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 18', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 17', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 16', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 15', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 14', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 13', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 12', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 11', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 10', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 9', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 8', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 7', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 6', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 5', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 4', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 3', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 2', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      }, {
                        keypath: 'Ã¥Â½Â¢Ã§ÂÂ¶Ã¥ÂÂ¾Ã¥Â±Â 1', // Replace with your specific layer name
                        color: theme.buttonBg, // Change to your desired color
                      },


                    ]}
                  />
                </View>
              }
            </View>
          </GestureDetector>


        </View>
        {(isPressing && isSwappingUp) && <View style={{ height: 60, width: windowWidth, paddingEnd: 40, paddingStart: 5, alignItems: 'center', flexDirection: 'row', justifyContent: 'space-between' }}>
          <TouchableOpacity
            onPress={uploadFileHndler}
            style={{ alignItems: 'center', justifyContent: 'center', height: 40, width: 40, borderRadius: 20, backgroundColor: theme.buttonBg }}

          >
            <PaperAirplaneIcon
              size={28}
              color={theme.centerChannelBg}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={{ alignItems: 'center', justifyContent: 'center', height: 40, width: 40, borderRadius: 20 }}
            onPress={deleteImageFile}
          >
            <TrashIcon
              size={28}
              color={theme.buttonBg}
            />
          </TouchableOpacity>

        </View>}
      </View>


    </GestureHandlerRootView>
  );
}
{/*</GestureRecognizer>*/ }




/* */
