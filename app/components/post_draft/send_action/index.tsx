// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useEffect, useMemo } from 'react';
import { Image, View } from 'react-native';

import CompassIcon from '@components/compass_icon';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import { useTheme } from '@context/theme';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import AudioRecorfderComponent from './AudioRecorfderComponent';
import { useIntl } from 'react-intl';
import { useServerUrl } from '@app/context/server';
import { PaperAirplaneIcon } from 'react-native-heroicons/outline'
import type { SharedValue } from 'react-native-reanimated';
type Props = {

    addFiles: (files: FileInfo[]) => void
    currentUserID: string;
    rootID: string;
    channelId: string;
    isHadFile: boolean;
    wordLength: number;
    isPressing: boolean;
    setIsPressing: (state: boolean) => void;
    testID: string;
    disabled: boolean;
    sendMessage: () => void;
    translationY?: SharedValue<number> | undefined;
    isVisibale?: SharedValue<boolean> | undefined;
    pageName: string

}

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return {
        disableButton: {
            backgroundColor: changeOpacity(theme.buttonBg, 0.3),
        },
        sendButtonContainer: {
            //justifyContent: 'flex-end',
            // paddingRight: 8,
            flex: 1,
            marginTop: 14, marginEnd: 5
            ,
        },
        sendButton: {
            backgroundColor: theme.buttonBg,
            borderRadius: 4,
            height: 32,
            width: 80,
            alignItems: 'center',
            justifyContent: 'center',
        },
    };
});

function SendButton({
    //changeRecodingState,
    //sendingFile ,
    addFiles,
    currentUserID,
    rootID,
    channelId,

    isHadFile,
    wordLength = 0,
    isPressing = false,
    setIsPressing,
    testID,
    disabled,
    sendMessage,
    translationY = undefined,
    isVisibale = undefined,
    pageName
}: Props) {
    const theme = useTheme();
    const intl = useIntl();
    const sendButtonTestID = disabled ? `${testID}.send.button.disabled` : `${testID}.send.button`;
    const style = getStyleSheet(theme);

    const viewStyle = useMemo(() => {
        if (disabled) {
            return [style.sendButton, style.disableButton];
        }
        return style.sendButton;
    }, [disabled, style]);

    const buttonColor = disabled ? changeOpacity(theme.buttonColor, 0.5) : theme.buttonColor;

    // useEffect(() => {
    //     if (isPressing === true) {
    //         setIsPressing(true);
    //     }

    //     if (isPressing === false) {
    //         setIsPressing(false);
    //     }


    //     if (wordLength > 0 || isHadFile === true) {
    //         setIsPressing(false)
    //     }
    // })

    // useEffect(() => {
    //     if (isPressing === true) {
    //         setIsPressing(true);
    //     }

    //     if (isPressing === false) {
    //         setIsPressing(false);
    //     }


    //     if (wordLength > 0 || isHadFile === true) {
    //         setIsPressing(false)
    //     }

    //    // console.log(`\n\nispressing ${isPressing}\n\n\n`)
    // }, [isPressing, wordLength, isHadFile])



    //  useEffect(()=>{

    //  },[sendMessage])



    return (
        <View
        // style={style.sendButtonContainer}
        >
            {(isHadFile || wordLength > 0) ? <TouchableWithFeedback
                testID={sendButtonTestID}
                onPress={sendMessage}
                style={style.sendButtonContainer}
                type={'opacity'}
                disabled={disabled}
            >
                <View
                    style={{
                        height: 40, width: 40
                        ,
                        marginStart: 3,
                        bottom: 6, left: 10,
                        justifyContent: 'center',
                    }}
                >
                    <PaperAirplaneIcon
                        //source={require("assets/base/images/send.png")}
                        color={disabled?changeOpacity(theme.buttonBg,0.16):theme.buttonBg}
                        height={40}
                    />
                </View>
            </TouchableWithFeedback> :
                <AudioRecorfderComponent
                    pageName={pageName}
                    translationYss={translationY}
                    isVisibale={isVisibale}

                    // changeRecodingState={changeRecodingState}

                    currentUserID={currentUserID}
                    channelId={channelId}
                    isPressing={isPressing}
                    changePressingState={setIsPressing}
                    sendingFile={addFiles}
                    rootID={rootID}
                />}
        </View>
    );
}
{/* */ }

export default SendButton;
