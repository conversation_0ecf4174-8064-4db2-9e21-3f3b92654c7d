// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Alert, StyleSheet, View } from 'react-native';

import CompassIcon from '@components/compass_icon';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import { ICON_SIZE } from '@constants/post_draft';
import { useTheme } from '@context/theme';
import { extractFileInfo, fileMaxWarning } from '@utils/file';
import PickerUtil from '@utils/file/file_picker';
import { changeOpacity } from '@utils/theme';
import { Audio, InterruptionModeAndroid, InterruptionModeIOS } from 'expo-av';

import type { QuickActionAttachmentProps } from '@typings/components/post_draft_quick_action';
import { TouchableOpacity } from 'react-native-gesture-handler';
import type { Recording } from 'expo-av/build/Audio';

import * as FileSystem from 'expo-file-system';
import { path } from 'lodash/fp';
import FilePickerUtil from '@utils/file/file_picker';
import type { DocumentPickerResponse } from 'react-native-document-picker';
import { createPost } from '@actions/remote/post';
import { uploadFile } from '@actions/remote/file';


const style = StyleSheet.create({
    icon: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
    },


});



export default function AudioQuickAction({
    disabled,
    onUploadFiles,
    maxFilesReached,
    maxFileCount,
    testID = '',
}: QuickActionAttachmentProps) {
    const intl = useIntl();
    const theme = useTheme();

    const actionTestID = disabled ? `${testID}.disabled` : testID;

    const [recording, setRecording] = useState<Recording>()

    // const [recording, setRecording] = useState<boolean>(false);
    const [permissionResponse, requestPermission] = Audio.usePermissions();
    // const audioRecorderPlayer = new AudioRecorderPlayer();

    //const [isRecording, setRecording] = useState(false);


    const color = disabled ? changeOpacity(theme.buttonBg, 0.16) :
        changeOpacity(theme.buttonBg, 0.64);



    async function startRecording() {
        try {
            // setAudioMetering([])

            await Audio.requestPermissionsAsync()
            await Audio.setAudioModeAsync({
                allowsRecordingIOS: true,
                playsInSilentModeIOS: true,
            })

            const { recording } = await Audio.Recording.createAsync(
                Audio.RecordingOptionsPresets.HIGH_QUALITY,
                undefined,
                100
            )
            setRecording(recording)
            // Debug log removed to prevent React Native text rendering errors

            //  recording.setOnRecordingStatusUpdate((status) => {
            //   if (status.metering) {
            //    metering.value = status.metering
            //    setAudioMetering((curVal) => [...curVal, status.metering || -100])
            //   }
            // })
        } catch (err) {
            console.error('Failed to start recording', err)
        }

    }

    async function stopRecording() {
        let uri: string | null = "";
        try {

            if (!recording) {
                return
            }

            setRecording(undefined)
            await recording.stopAndUnloadAsync()
            await Audio.setAudioModeAsync({
                allowsRecordingIOS: false,
            })
            uri = recording.getURI()
         //   console.log(`\n\n\n\n\n\n this the file uri is ${uri} \n\n\n\n\n\n`)
            // Reset metering to cancel the wave animation

        } catch (error) {

            setRecording(undefined);
        }
        if (uri !== null || uri !== "") {

          //  const { sound } = await Audio.Sound.createAsync({
          //      uri: uri!!,
          //  });
         //   await sound.playAsync();

        //    console.log(`\n\n\n\n\n\n end playing recording  \n\n\n\n\n\n`)
            uploadFileHndler(uri!!)
        }
    }


    function handleClickButton() {

        if (recording) {
            // Debug log removed to prevent React Native text rendering errors
            stopRecording()
            return;
        } else {

            // Debug log removed to prevent React Native text rendering errors
            startRecording()
        }

    }


    function uploadFileHndler(fileUri: string) {
        FileSystem.getInfoAsync(fileUri)//.exists(filepath)

            .then(async (result) => {
                var fileNameToList = fileUri.split('/');
                var file = new FilePickerUtil(intl, onUploadFiles);

                var fileInfo = {
                    uri: result.uri,
                    name: fileNameToList[fileNameToList.length - 2],
                    copyError: null,
                    fileCopyUri: null,
                    type: fileNameToList[fileNameToList.length - 1],
                    size: null
                } as unknown as DocumentPickerResponse;
                  file.prepareFileUploadAudio(fileInfo)

                // const postFiles = files.filter((f) => !f.failed);
             //   let filesList: DocumentPickerResponse[] = []
              //  filesList.push(fileInfo)
              //  let resultData = await extractFileInfo(filesList);


             //   uploadFile(serverUrl!!, resultData[0], channelId!!)


            });
           // deleteImageFile(fileUri!!.toString())

    }


    return (
        <TouchableOpacity
            testID={actionTestID}
            disabled={disabled}
            onPress={handleClickButton}
            style={style.icon}
        // type={'microphone-outline'}
        >
            <View
                style={{
                    /*backgroundColor: recording && theme.buttonBg,
                    borderRadius: recording && 50, height: 30, width: 30,
                    alignItems: recording && 'center',
                    justifyContent: recording && 'center',
                    marginBottom: recording && 8*/
                    backgroundColor: recording && theme.buttonBg,
                    borderRadius: recording && 50, height: 30, width: 30,
                    alignItems: recording && 'center',
                    justifyContent: recording && 'center',
                    marginBottom: recording && 8

                }}
            >

                <CompassIcon
                    color={recording ? changeOpacity(theme.mentionHighlightBg, 0.80) : color}
                    name='microphone-outline'
                    size={ICON_SIZE}
                />
            </View>
        </TouchableOpacity>
    );
}

