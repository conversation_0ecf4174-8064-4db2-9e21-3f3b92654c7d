// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
import * as React from 'react';
import Svg, {
    Path,
    Ellipse,
} from 'react-native-svg';

type Props = {
    theme: Theme;
}

function ShareFeedbackIllustration({theme}: Props) {
    return (
        <Svg
            width={172}
            height={172}
            viewBox='0 0 172 172'
            fill='none'
        >
            <Path
                d='M39.982 33h92.003a12.865 12.865 0 0 1 9.092 3.722 12.805 12.805 0 0 1 3.791 9.047v58.35a12.801 12.801 0 0 1-3.791 9.047 12.866 12.866 0 0 1-9.092 3.722h-13.577v21.841l-20.367-21.841H40.015a12.859 12.859 0 0 1-9.091-3.722 12.81 12.81 0 0 1-3.792-9.047v-58.35a12.799 12.799 0 0 1 3.78-9.035A12.852 12.852 0 0 1 39.982 33Z'
                fill='#FFBC1F'
            />
            <Path
                d='M98.04 116.888H40.016a12.857 12.857 0 0 1-9.091-3.722 12.81 12.81 0 0 1-3.792-9.047V68.695s4.052 32.757 4.78 35.64c.727 2.884 2.172 7.198 9.015 7.913 6.843.716 57.114 4.64 57.114 4.64Z'
                fill='#CC8F00'
            />
            <Path
                d='M117.408 66.603a8.3 8.3 0 0 0-4.604 1.393 8.255 8.255 0 0 0-1.256 12.725 8.302 8.302 0 0 0 12.752-1.253 8.263 8.263 0 0 0 .768-7.762 8.255 8.255 0 0 0-4.486-4.477 8.295 8.295 0 0 0-3.174-.626ZM85.983 66.603a8.3 8.3 0 0 0-4.605 1.393 8.275 8.275 0 0 0-3.052 3.712 8.255 8.255 0 0 0 1.797 9.013 8.304 8.304 0 0 0 9.032 1.793 8.285 8.285 0 0 0 3.72-3.046 8.258 8.258 0 0 0 1.396-4.595 8.243 8.243 0 0 0-2.424-5.851 8.277 8.277 0 0 0-5.864-2.42ZM54.592 66.603a8.3 8.3 0 0 0-4.607 1.388 8.274 8.274 0 0 0-3.057 3.71 8.254 8.254 0 0 0 1.79 9.017 8.294 8.294 0 0 0 9.032 1.797 8.284 8.284 0 0 0 3.722-3.046 8.258 8.258 0 0 0 1.397-4.596 8.246 8.246 0 0 0-2.42-5.847 8.278 8.278 0 0 0-5.857-2.423Z'
                fill={theme.centerChannelBg}
            />
            <Path
                d='M135.32 57.433a25.992 25.992 0 0 0-4.65-9.077 26.044 26.044 0 0 0-7.788-6.597.902.902 0 0 1-.474-.994.897.897 0 0 1 .844-.708c5.8-.347 17.51.889 13.838 17.289a.912.912 0 0 1-1.77.087Z'
                fill='#FFD470'
            />
            <Ellipse
                cx={86}
                cy={148.925}
                rx={52.528}
                ry={4.075}
                fill='#000'
                fillOpacity={0.08}
            />
        </Svg>
    );
}

export default ShareFeedbackIllustration;
