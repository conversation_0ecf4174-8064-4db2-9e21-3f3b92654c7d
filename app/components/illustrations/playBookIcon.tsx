import { useTheme } from '@app/context/theme'
import React from 'react'
import Svg, { Path, Rect } from 'react-native-svg'

const PlayBookIcon = () => {
    const theme = useTheme()
    return (
        <Svg width="28" height="28" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Rect width="64" height="64" />
            <Path d="M23.984 15.208H20C18.912 15.208 17.968 15.592 17.168 16.36C16.4 17.128 16.016 18.072 16.016 19.192V46.216C16.016 47.304 16.4 48.232 17.168 49C17.968 49.8 18.912 50.2 20 50.2H44C45.12 50.2 46.064 49.8 46.832 49C47.6 48.232 47.984 47.304 47.984 46.216V19.192C47.984 18.072 47.6 17.128 46.832 16.36C46.064 15.592 45.12 15.208 44 15.208H40.016V18.184H42.992C43.536 18.184 44 18.392 44.384 18.808C44.8 19.192 45.008 19.656 45.008 20.2V45.208C45.008 45.752 44.8 46.216 44.384 46.6C44 46.984 43.536 47.176 42.992 47.176H21.008C20.464 47.176 19.984 46.984 19.568 46.6C19.184 46.216 18.992 45.752 18.992 45.208V20.2C18.992 19.656 19.184 19.192 19.568 18.808C19.984 18.392 20.464 18.184 21.008 18.184H23.984V15.208ZM29.984 12.184C29.728 12.184 29.504 12.296 29.312 12.52C29.12 12.712 29.024 12.936 29.024 13.192V14.2H26V19.192H38V14.2H35.024V13.192C35.024 12.936 34.912 12.712 34.688 12.52C34.496 12.296 34.272 12.184 34.016 12.184H29.984ZM29.024 40.216H41.984V42.184H29.024V40.216ZM23.024 39.688H26V42.712H23.024V39.688ZM29.024 32.2H41.984V34.216H29.024V32.2ZM23.024 31.72H26V34.696H23.024V31.72ZM29.024 24.184H41.984V26.2H29.024V24.184ZM22.832 26.392L21.824 25.384L22.832 24.376L23.84 25.384L26 23.176L27.008 24.184L23.84 27.4L22.832 26.392Z" 
            fill={theme.sidebarHeaderTextColor}
            />
        </Svg>

    )
}

export default PlayBookIcon
