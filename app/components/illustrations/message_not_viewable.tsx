// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
import * as React from 'react';
import Svg, {Path, Ellipse} from 'react-native-svg';

type Props = {
    theme: Theme;
}

function SvgComponent({theme}: Props) {
    return (
        <Svg
            width={180}
            height={157}
            viewBox='0 0 180 157'
            fill='none'
        >
            <Path
                d='M127.838 40.436h-75.65a10.577 10.577 0 00-7.475 3.06 10.533 10.533 0 00-3.117 7.44v47.977a10.513 10.513 0 003.117 7.439 10.576 10.576 0 007.475 3.061h11.165v17.959l16.746-17.959h47.712a10.573 10.573 0 007.476-3.061 10.516 10.516 0 003.117-7.439V50.937a10.521 10.521 0 00-3.108-7.43 10.567 10.567 0 00-7.458-3.07z'
                fill='#1C58D9'
            />
            <Path
                d='M80.1 109.413h47.712a10.568 10.568 0 007.475-3.061 10.523 10.523 0 003.117-7.439V69.787s-3.331 26.935-3.929 29.306c-.599 2.371-1.787 5.918-7.414 6.506-5.626.588-46.961 3.815-46.961 3.815z'
                fill={theme.centerChannelColor}
                fillOpacity={0.16}
            />
            <Path
                d='M64.174 68.066c1.348 0 2.665.399 3.786 1.146a6.787 6.787 0 011.033 10.463 6.82 6.82 0 01-7.427 1.474 6.812 6.812 0 01-3.058-2.504 6.79 6.79 0 01-.632-6.382 6.792 6.792 0 013.689-3.682 6.82 6.82 0 012.61-.515zM90.014 68.066c1.348 0 2.665.399 3.786 1.146a6.804 6.804 0 012.51 3.052 6.787 6.787 0 01-1.477 7.411 6.819 6.819 0 01-7.427 1.474 6.812 6.812 0 01-3.058-2.504 6.79 6.79 0 01-.632-6.382 6.79 6.79 0 013.689-3.682 6.818 6.818 0 012.609-.515zM115.825 68.066a6.825 6.825 0 013.788 1.142 6.802 6.802 0 012.906 6.98 6.802 6.802 0 01-5.352 5.348 6.829 6.829 0 01-6.999-2.89 6.792 6.792 0 01-.633-6.381 6.781 6.781 0 013.683-3.681 6.82 6.82 0 012.607-.518z'
                fill={theme.centerChannelBg}
            />
            <Path
                d='M49.447 60.526a21.376 21.376 0 013.823-7.464 21.424 21.424 0 016.403-5.424.74.74 0 00-.304-1.4c-4.769-.285-14.397.731-11.378 14.217a.748.748 0 00.7.606.752.752 0 00.755-.535z'
                fill={theme.centerChannelBg}
                fillOpacity={0.16}
            />
            <Path
                d='M114.364 62.33c-2.475 0-3.597-1.785-2.492-3.966L131.88 19.06c1.133-2.175 2.917-2.175 4.028 0l20.002 39.303c1.133 2.175 0 3.965-2.493 3.965h-39.053z'
                fill='#FFBC1F'
            />
            <Path
                d='M130.753 33.597l2.056 13.816a1.09 1.09 0 001.826.718 1.09 1.09 0 00.344-.718l2.056-13.816c.374-5.387-6.662-5.387-6.282 0zM133.891 49.912a3.148 3.148 0 013.083 3.767 3.153 3.153 0 01-2.476 2.472 3.149 3.149 0 11-.607-6.24z'
                fill='#2D3039'
            />
            <Ellipse
                cx={90.5}
                cy={131}
                rx={62.5}
                ry={3}
                fill={theme.centerChannelColor}
                fillOpacity={0.06}
            />
        </Svg>
    );
}

export default SvgComponent;
