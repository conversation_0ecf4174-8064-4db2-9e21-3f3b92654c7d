// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Loading Error should match snapshot 1`] = `
<View
  style={
    {
      "alignItems": "center",
      "flex": 1,
      "justifyContent": "center",
      "padding": 20,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "rgba(255,255,255,0.08)",
        "borderRadius": 60,
        "height": 120,
        "justifyContent": "center",
        "width": 120,
      }
    }
  >
    <Icon
      name="alert-circle-outline"
      style={
        {
          "color": "rgba(255,255,255,0.48)",
          "fontSize": 72,
          "lineHeight": 72,
        }
      }
    />
  </View>
  <Text
    style={
      [
        {
          "fontFamily": "Metropolis-SemiBold",
          "fontSize": 20,
          "fontWeight": "600",
          "lineHeight": 28,
        },
        {
          "color": "#ffffff",
          "marginTop": 20,
          "textAlign": "center",
        },
      ]
    }
  >
    Error title
  </Text>
  <Text
    style={
      [
        {
          "fontFamily": "IBMPlexSansArabic",
          "fontSize": 16,
          "fontWeight": "400",
          "lineHeight": 24,
        },
        {
          "color": "#ffffff",
          "marginTop": 4,
          "textAlign": "center",
        },
      ]
    }
  >
    Error description
  </Text>
  <View
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": undefined,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#ffffff",
        "borderRadius": 4,
        "flex": 0,
        "height": 48,
        "justifyContent": "center",
        "marginTop": 24,
        "opacity": 1,
        "paddingHorizontal": 24,
        "paddingVertical": 14,
      }
    }
  >
    <Text
      style={
        [
          {
            "alignItems": "center",
            "fontFamily": "IBMPlexSansArabic-SemiBold",
            "fontWeight": "600",
            "justifyContent": "center",
            "padding": 1,
            "textAlignVertical": "center",
          },
          {
            "fontSize": 16,
            "lineHeight": 18,
            "marginTop": 1,
          },
          {
            "color": "#1c58d9",
          },
        ]
      }
    >
      Retry
    </Text>
  </View>
</View>
`;
