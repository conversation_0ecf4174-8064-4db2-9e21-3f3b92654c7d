// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import type ClientBase from './base';
export interface ClientAIMix {
    gerateImage: (userId: string, preferences: PreferenceType[]) => Promise<any>;
}

const ClientAI = <TBase extends Constructor<ClientBase>>(superclass: TBase) => class extends superclass {
    gerateImage = async (userId: string, preferences: PreferenceType[]) => {
        return this.doFetch(
            `${this.getPreferencesRoute(userId)}/delete`,
            {method: 'post', body: preferences},
        );
    };
};

export default ClientAI;
