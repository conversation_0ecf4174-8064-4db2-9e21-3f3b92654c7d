// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {BottomSheetFooter, type BottomSheetFooterProps, SHEET_STATE, useBottomSheet, useBottomSheetInternal} from '@gorhom/bottom-sheet';
import React, {useCallback} from 'react';
import {Platform, View, TouchableOpacity} from 'react-native';
import Animated, {useAnimatedStyle, withTiming} from 'react-native-reanimated';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {useKeyboardHeight} from '@hooks/device';
import {selectEmojiCategoryBarSection} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBar from '../emoji_category_bar';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
    },
    categoryBarContainer: {
        flex: 1,
    },
    deleteButton: {
        backgroundColor: changeOpacity(theme.errorTextColor, 0.1),
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        minWidth: 44,
        height: 32,
        alignItems: "center",
        justifyContent: "center",
        marginLeft: 8,
        borderWidth: 1,
        borderColor: changeOpacity(theme.errorTextColor, 0.2),
    },
    deleteButtonIcon: {
        color: theme.errorTextColor,
    },
}));

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type PickerFooterProps = BottomSheetFooterProps & {
    selectedEmojis?: SelectedEmoji[];
    onRemoveAllEmojis?: () => void;
};

const PickerFooter = ({selectedEmojis = [], onRemoveAllEmojis, ...props}: PickerFooterProps) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const keyboardHeight = useKeyboardHeight();
    const {animatedSheetState} = useBottomSheetInternal();
    const {expand} = useBottomSheet();

    const scrollToIndex = useCallback((index: number) => {
        if (animatedSheetState.value === SHEET_STATE.EXTENDED) {
            selectEmojiCategoryBarSection(index);
            return;
        }
        expand();

        // @ts-expect-error wait until the bottom sheet is epanded
        while (animatedSheetState.value !== SHEET_STATE.EXTENDED) {
            // do nothing
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    const handleDeleteAll = useCallback(() => {
        onRemoveAllEmojis?.();
    }, [onRemoveAllEmojis]);

    const animatedStyle = useAnimatedStyle(() => {
        const paddingBottom = withTiming(
            Platform.OS === 'ios' ? 20 : 0,
            {duration: 250},
        );
        return {backgroundColor: theme.centerChannelBg, paddingBottom};
    }, [theme]);

    const heightAnimatedStyle = useAnimatedStyle(() => {
        let height = 55;
        if (keyboardHeight === 0 && Platform.OS === 'ios') {
            height += 20;
        } else if (keyboardHeight) {
            height = 0;
        }

        return {
            height,
        };
    }, [keyboardHeight]);



    return (
        <BottomSheetFooter
            style={heightAnimatedStyle}
            {...props}
        >
            <Animated.View style={[animatedStyle]}>
                <View style={styles.container}>
                    <View style={styles.categoryBarContainer}>
                        <EmojiCategoryBar onSelect={scrollToIndex}/>
                    </View>
                    {selectedEmojis.length > 0 && (
                        <TouchableOpacity
                            style={styles.deleteButton}
                            onPress={handleDeleteAll}
                            testID="emoji_picker.delete_all_button"
                            activeOpacity={0.8}
                            accessibilityLabel="Delete all selected emojis"
                            accessibilityRole="button"
                            accessibilityHint="Removes all selected emojis from the preview"
                        >
                            <CompassIcon
                                name="trash-can-outline"
                                size={16}
                                style={styles.deleteButtonIcon}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </Animated.View>
        </BottomSheetFooter>
    );
};

export default PickerFooter;
