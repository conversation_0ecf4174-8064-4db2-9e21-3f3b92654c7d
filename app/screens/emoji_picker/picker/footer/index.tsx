// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {BottomSheetFooter, type BottomSheetFooterProps, SHEET_STATE, useBottomSheet, useBottomSheetInternal} from '@gorhom/bottom-sheet';
import React, {useCallback} from 'react';
import {Platform, View, TouchableOpacity} from 'react-native';
import Animated, {useAnimatedStyle, withTiming} from 'react-native-reanimated';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {useKeyboardHeight} from '@hooks/device';
import {selectEmojiCategoryBarSection} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';


import EmojiCategoryBar from '../emoji_category_bar';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
    },
    categoryBarContainer: {
        flex: 1,
    },
    backspaceButton: {
        width: 32,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderRadius: 4,
        marginLeft: 8,
    },
    backspaceIcon: {
        color: changeOpacity(theme.centerChannelColor, 0.7),
    },
}));

type PickerFooterProps = BottomSheetFooterProps & {
    selectedEmojis?: SelectedEmoji[];
    cursorPosition?: number;
    onRemoveEmojiAtPosition?: (position: number) => void;
};

const PickerFooter = ({selectedEmojis = [], cursorPosition = 0, onRemoveEmojiAtPosition, ...props}: PickerFooterProps) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const keyboardHeight = useKeyboardHeight();
    const {animatedSheetState} = useBottomSheetInternal();
    const {expand} = useBottomSheet();

    const scrollToIndex = useCallback((index: number) => {
        if (animatedSheetState.value === SHEET_STATE.EXTENDED) {
            selectEmojiCategoryBarSection(index);
            return;
        }
        expand();

        // @ts-expect-error wait until the bottom sheet is epanded
        while (animatedSheetState.value !== SHEET_STATE.EXTENDED) {
            // do nothing
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    // Handle backspace button press
    const handleBackspacePress = useCallback(() => {
        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            // Delete emoji at cursor position (or the one before cursor if cursor is at end)
            const deletePosition = cursorPosition > 0 ? cursorPosition - 1 : 0;
            if (deletePosition < selectedEmojis.length) {
                onRemoveEmojiAtPosition(deletePosition);
            }
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition]);



    const animatedStyle = useAnimatedStyle(() => {
        const paddingBottom = withTiming(
            Platform.OS === 'ios' ? 20 : 0,
            {duration: 250},
        );
        return {backgroundColor: theme.centerChannelBg, paddingBottom};
    }, [theme]);

    const heightAnimatedStyle = useAnimatedStyle(() => {
        let height = 55;
        if (keyboardHeight === 0 && Platform.OS === 'ios') {
            height += 20;
        } else if (keyboardHeight) {
            height = 0;
        }

        return {
            height,
        };
    }, [keyboardHeight]);



    return (
        <BottomSheetFooter
            style={heightAnimatedStyle}
            {...props}
        >
            <Animated.View style={[animatedStyle]}>
                <View style={styles.container}>
                    <View style={styles.categoryBarContainer}>
                        <EmojiCategoryBar onSelect={scrollToIndex}/>
                    </View>
                    {selectedEmojis.length > 0 && (
                        <TouchableOpacity
                            style={styles.backspaceButton}
                            onPress={handleBackspacePress}
                            testID="emoji_picker.footer.backspace_button"
                            activeOpacity={0.8}
                            accessibilityLabel="Delete emoji at cursor position"
                            accessibilityRole="button"
                            accessibilityHint="Deletes the emoji at the current cursor position"
                        >
                            <CompassIcon
                                name="trash-can-outline"
                                size={20}
                                style={styles.backspaceIcon}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </Animated.View>
        </BottomSheetFooter>
    );
};

export default PickerFooter;
