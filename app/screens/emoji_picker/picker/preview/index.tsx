// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useRef, useEffect, useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Platform,
    Animated,
    TextInput,
} from "react-native";




import CompassIcon from "@components/compass_icon";
import { useTheme } from "@context/theme";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";
import { typography } from "@utils/typography";

import type { SelectedEmoji } from "@components/post_draft/emoji_preview";

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.16),
            paddingHorizontal: 8,
            paddingVertical: 5,
            maxHeight: 100,
            minHeight: 100,
            marginHorizontal: 8,
            marginVertical: 8,
            // ...Platform.select({
            //     ios: {
            //         shadowColor: '#000',
            //         shadowOffset: { width: 0, height: 2 },
            //         shadowOpacity: 0.1,
            //         shadowRadius: 6,
            //     },
            //     android: {
            //         elevation: 3,
            //     },
            // }),
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 12,
        },
        title: {
            color: theme.centerChannelColor,
            ...typography('Heading', 100, 'SemiBold'),
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
            minWidth: 64,
            height: 32,
            alignItems: "center",
            justifyContent: "center",
            ...Platform.select({
                ios: {
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.2,
                    shadowRadius: 3,
                },
                android: {
                    elevation: 2,
                },
            }),
        },
        doneButtonText: {
            color: theme.buttonColor,
            ...typography('Body', 75, 'SemiBold'),
        },
        textInputContainer: {
            flex: 1,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.02),
        },
        textInput: {
            fontSize: 24,
            color: theme.centerChannelColor,
            paddingVertical: 8,
            paddingHorizontal: 8,
            minHeight: 50,
            textAlignVertical: 'top',
            lineHeight: 32,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.56),
            textAlign: "center",
            flex: 1,
            paddingVertical: 16,
            ...typography('Body', 75, 'Regular'),
        },
        emojiItem: {
            position: "relative",
            marginRight: 8,
            minWidth: 40,
            height: 40,
            flexShrink: 0,
            alignItems: "center",
            justifyContent: "center",
        },
        emojiCharacter: {
            fontSize: 24,
            textAlign: "center",
        },
        removeButton: {
            position: "absolute",
            top: -2,
            right: -2,
            width: 18,
            height: 18,
            borderRadius: 9,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.8),
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1,
        },
        backspaceButton: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 12,
            paddingVertical: 8,
            borderRadius: 8,
            minWidth: 44,
            height: 32,
            alignItems: "center",
            justifyContent: "center",
            marginTop: 10,
            top: 270,
            right: 270,
            zIndex: 100,
            alignSelf: "flex-end",
            borderWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.16),
        },
        backspaceIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
            zIndex: 101,
        },

    });
});

const EmojiPickerPreview = ({
    selectedEmojis,
    onRemoveEmoji,
    onRemoveEmojiAtPosition,
    onDone,
    testID = "emoji_picker_preview",
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const textInputRef = useRef<TextInput>(null);

    // State for cursor position and text content
    const [cursorPosition, setCursorPosition] = useState(0);
    const [textValue, setTextValue] = useState('');

    // Animation values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.95)).current;

    // Update text value when emojis change
    useEffect(() => {
        const emojiText = selectedEmojis.map(emoji => emoji.character).join('');
        setTextValue(emojiText);
    }, [selectedEmojis]);

    // Animate in when component mounts
    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, scaleAnim]);



    const handleDone = useCallback(() => {
        // Add a subtle press animation
        Animated.sequence([
            Animated.timing(scaleAnim, {
                toValue: 0.96,
                duration: 100,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onDone();
        });
    }, [onDone, scaleAnim]);

    // Handle text input changes (mainly for backspace/delete functionality)
    const handleTextChange = useCallback((text: string) => {
        // If text is shorter than current text, user pressed backspace
        if (text.length < textValue.length && onRemoveEmojiAtPosition) {
            // Calculate which emoji was deleted based on cursor position
            const deletedPosition = Math.max(0, cursorPosition - 1);
            if (deletedPosition < selectedEmojis.length) {
                onRemoveEmojiAtPosition(deletedPosition);
            }
        }
    }, [textValue, cursorPosition, selectedEmojis, onRemoveEmojiAtPosition]);

    // Handle cursor position changes
    const handleSelectionChange = useCallback((event: any) => {
        const { start } = event.nativeEvent.selection;
        setCursorPosition(start);
    }, []);

    // Handle backspace button press
    const handleBackspacePress = useCallback(() => {
        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            // Delete emoji at cursor position (or the one before cursor if cursor is at end)
            const deletePosition = cursorPosition > 0 ? cursorPosition - 1 : 0;
            if (deletePosition < selectedEmojis.length) {
                onRemoveEmojiAtPosition(deletePosition);
                // Update cursor position after deletion
                const newCursorPosition = Math.max(0, cursorPosition - 1);
                setCursorPosition(newCursorPosition);

                // Focus the text input and set cursor position
                setTimeout(() => {
                    textInputRef.current?.focus();
                    textInputRef.current?.setNativeProps({
                        selection: { start: newCursorPosition, end: newCursorPosition }
                    });
                }, 50);
            }
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition]);











    if (selectedEmojis.length === 0) {
        return (
            <Animated.View
                style={[
                    styles.container,
                    {
                        opacity: fadeAnim,
                        transform: [{ scale: scaleAnim }]
                    }
                ]}
                testID={`${testID}.container`}
            >
                {/* <View style={styles.header}>
                    <Text style={styles.title}>Selected (0)</Text>
                </View>
                <Text style={styles.emptyText}>Tap emojis to select them</Text> */}
            </Animated.View>
        );
    }

    return (
        <Animated.View
            style={[
                styles.container,
                {
                    opacity: fadeAnim,
                    transform: [{ scale: scaleAnim }]
                }
            ]}
            testID={`${testID}.container`}
        >
            <View style={styles.header}>
                {/* <Text style={styles.title}>
                    Selected ({selectedEmojis.length})
                </Text> */}
                <TouchableOpacity
                    style={styles.doneButton}
                    onPress={handleDone}
                    testID={`${testID}.done_button`}
                    activeOpacity={0.8}
                    delayPressIn={0}
                    delayPressOut={50}
                >
                    <Text style={styles.doneButtonText}>تم</Text>
                </TouchableOpacity>

            </View>
            <View style={styles.textInputContainer}>
                <TextInput
                    ref={textInputRef}
                    style={styles.textInput}
                    value={textValue}
                    onChangeText={handleTextChange}
                    onSelectionChange={handleSelectionChange}
                    multiline={true}
                    editable={true}
                    showSoftInputOnFocus={false}
                    testID={`${testID}.text_input`}
                    selectionColor={theme.buttonBg}
                />
                {selectedEmojis.length > 0 && (
                    <TouchableOpacity
                        style={styles.backspaceButton}
                        onPress={handleBackspacePress}
                        testID={`${testID}.backspace_button`}
                        activeOpacity={0.8}
                        accessibilityLabel="Delete emoji at cursor position"
                        accessibilityRole="button"
                        accessibilityHint="Deletes the emoji at the current cursor position"
                    >
                        <CompassIcon
                            name="trash-can-outline"
                            size={16}
                            style={styles.backspaceIcon}
                        />
                    </TouchableOpacity>
                )}
            </View>
        </Animated.View>
    );
};

export default EmojiPickerPreview;
