
import {fetchPostAuthors} from '@actions/remote/post';
import {ActionType, Post} from '@constants';
import {MM_TABLES} from '@constants/database';
import DatabaseManager from '@database/manager';
import {logError} from '@utils/log';
import { createPostReplay, getActiveReplplayPostAtChannel ,getChannelActiveReplays} from '@app/queries/servers/postReplay';
import PostReplayModel from '@typings/database/models/servers/postReplay';
import post from '@app/constants/post';

const {SERVER: {REPLAY}} = MM_TABLES;

 

export async function disapleReplayPost(serverUrl: string, channelId: string, rootId: string, clientId: string, prepareRecordsOnly = false) {
    try {
        const {database, operator} = DatabaseManager.getServerDatabaseAndOperator(serverUrl);
        const postReplayHolder = await getActiveReplplayPostAtChannel(database, channelId);
        if (!postReplayHolder) {
            return {error: 'no draft'};
        }


        postReplayHolder.prepareUpdate((d) => {
                d.isFinished=true;
            });
        

        if (!prepareRecordsOnly) {
            await operator.batchRecords([postReplayHolder], 'removeDraftFile');
        }

        return {postReplayHolder};
    } catch (error) {
        logError('Failed removeDraftFile', error);
        return {error};
    }
}


export async function addPostReplay(serverUrl: string, channelId: string, postId:string) {
    try {
        const {database, operator} = DatabaseManager.getServerDatabaseAndOperator(serverUrl);
        const postReplayHolder = await getActiveReplplayPostAtChannel(database, channelId,);
     console.log(`\n\nthis the create postreplay channel id ${channelId}\n\n`)
        if (!postReplayHolder) {
            console.log("this from create new post replay draft")
            const newPostReplay: PostReplay = {
                channel_id: channelId,
                post_id:postId,
                isFinished:false,
            };

            createPostReplay(database,channelId,postId) 
            return true
        }

        postReplayHolder.prepareUpdate((d) => {
           d.postId = postId;
           d.isFinished = false
        });
            console.log("this from update post replay draft")

        //if (!postReplayHolder) {
            await operator.batchRecords([postReplayHolder], 'addFilesToDraft');
       // }

        return true;
    } catch (error) {
        logError('Failed addFilesToDraft', error);
                    console.log(`this eror from create  post replay draft ${error}`)

        return false;
    }
}

export const removeReplayPosts = async (serverUrl: string, channelId: string,  ) => {
    try {
        const {database} = DatabaseManager.getServerDatabaseAndOperator(serverUrl);
        const replayPostList = await getChannelActiveReplays(database, channelId);
        if (replayPostList!==undefined) {
            await database.write(async () => {
                replayPostList.forEach(async(x)=>await x.destroyPermanently())
                
            });
        }

        return {replayPostList};
    } catch (error) {
        logError('Failed removeDraft', error);
        return {error};
    }
};
