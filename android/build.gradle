buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        supportLibVersion = "33.0.0"
        kotlinVersion = "1.9.22"
        kotlin_version = kotlinVersion
        RNNKotlinVersion = kotlinVersion
        firebaseVersion = "24.0.0"
        ndkVersion = "26.1.10909125"
        
    }
    repositories {
        mavenCentral()
        mavenLocal()
        google()
        maven { url "https://jitpack.io" }
        
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.0')
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }

    configurations.classpath {
        resolutionStrategy.activateDependencyLocking()
    }
}

allprojects {
    repositories {
        maven {
            url "$rootDir/../node_modules/detox/Detox-android"
            maven { url "https://jitpack.io" }
        }

          maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
    }
}

apply plugin: "com.facebook.react.rootproject"

dependencyLocking {
    lockAllConfigurations()
}
