{"project_info": {"project_number": "184930218130", "firebase_url": "https://api-7231322553409637977-752355.firebaseio.com", "project_id": "api-7231322553409637977-752355", "storage_bucket": "api-7231322553409637977-752355.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:184930218130:android:a0e553d1a1f043b5", "android_client_info": {"package_name": "com.mattermost.react.native"}}, "oauth_client": [{"client_id": "184930218130-8nahrspll1opff0uogtkh2qsv8coqngo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCkZkU2ECVg-mmdCG5OoHHQXtKiENuvWPE"}], "services": {"analytics_service": {"status": 2}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:184930218130:android:c7debfa7ea3f75a7", "android_client_info": {"package_name": "com.mattermost.rnbeta.fack"}}, "oauth_client": [{"client_id": "184930218130-8nahrspll1opff0uogtkh2qsv8coqngo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCkZkU2ECVg-mmdCG5OoHHQXtKiENuvWPE"}], "services": {"analytics_service": {"status": 2}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:184930218130:android:2db4058a5b5d6506", "android_client_info": {"package_name": "com.mattermost.rn"}}, "oauth_client": [{"client_id": "184930218130-8nahrspll1opff0uogtkh2qsv8coqngo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCkZkU2ECVg-mmdCG5OoHHQXtKiENuvWPE"}], "services": {"analytics_service": {"status": 2}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}], "configuration_version": "1"}