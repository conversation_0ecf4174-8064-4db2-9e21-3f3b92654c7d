package com.mattermost.helpers;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;

/**
 * ResolvePromise: Helper class that abstracts boilerplate
 */
public class ResolvePromise implements Promise {
    @Override
    public void resolve(@javax.annotation.Nullable Object value) {

    }

    @Override
    public void reject(String code, String message) {

    }

    @Override
    public void reject(String code, @NonNull WritableMap map) {

    }

    @Override
    public void reject(String code, Throwable e) {

    }

    @Override
    public void reject(Throwable e, WritableMap map) {

    }

    @Override
    public void reject(String code, Throwable e, WritableMap map) {

    }

    @Override
    public void reject(String code, String message, Throwable e, WritableMap map) {

    }

    @Override
    public void reject(String code, String message, Throwable e) {

    }

    @Override
    public void reject(String code, String message, @NonNull WritableMap map) {

    }

    @Override
    public void reject(String message) {

    }

    @Override
    public void reject(Throwable reason) {

    }
}
