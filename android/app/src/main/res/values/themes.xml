<!-- /android/app/src/main/res/values/styles.xml -->
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- 1) Base application theme using Material3. -->
    <style name="Base.AppTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Apply this Dialog.Theme to the TimePicker. -->
        <item name="android:timePickerDialogTheme">@style/Dialog.Theme</item>

        <!-- Also apply this style if you want the same for the DatePicker: -->
        <item name="android:datePickerDialogTheme">@style/Dialog.Theme</item>

        <!-- Ensure "OK" and "Cancel" in the dialogs are styled with MyDialogButtonStyle. -->
        <item name="android:buttonBarPositiveButtonStyle">@style/MyDialogButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/MyDialogButtonStyle</item>

    </style>

    <!-- 2) Dialog theme that uses Material 3 Alert style. -->
    <style name="Dialog.Theme" parent="Theme.Material3.DayNight.Dialog.Alert">
        <!-- Optional: the header color if you want a teal top bar. -->
        <item name="colorPrimary">#03997f</item>
        <item name="colorOnPrimary">#FFFFFF</item>

        <!-- If you still need accent or text color changes within the dialog: -->
        <item name="colorAccent">#03997f</item>
        <item name="android:textColorPrimary">#03997f</item>

        <!-- Also define the button styles HERE, to ensure they apply: -->
        <item name="android:buttonBarPositiveButtonStyle">@style/MyDialogButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/MyDialogButtonStyle</item>
    </style>

    <!-- 3) The final app theme that inherits from Base.AppTheme. -->
    <style name="AppTheme" parent="Base.AppTheme" />

    <!-- 4) Style for the OK/Cancel buttons to make their text teal. -->
    <style name="MyDialogButtonStyle" parent="Widget.Material3.Button.TextButton.Dialog">
        <item name="android:textColor">#03997f</item>
    </style>



</resources>
