<resources>
    <string name="app_name">Sofa Workspace</string>
    <string name="inAppPinCode_title">in-App Pincode</string>
    <string name="inAppPinCode_description">Require users to authenticate as the owner of the phone before using the app. Prompts for fingerprint or passcode when the app first opens and when the app has been in the background for more than 5 minutes.</string>
    <string name="blurApplicationScreen_title">Blur Application Screen</string>
    <string name="blurApplicationScreen_description">Blur the app when it’s set to background to protect any confidential on-screen information, it also prevents taking screenshots of the app.</string>
    <string name="jailbreakProtection_title">Jailbreak &#x2F; Root Detection</string>
    <string name="jailbreakProtection_description">Disable app launch on Jailbroken or rooted devices.</string>
    <string name="copyAndPasteProtection_title">Copy&amp;Paste Protection</string>
    <string name="copyAndPasteProtection_description">Disable the ability to copy from or paste into any text inputs in the app.</string>
    <string name="serverUrl_title">Sofa Server URL</string>
    <string name="serverUrl_description">Set a default Server URL for your Sofa instance.</string>
    <string name="serverName_title">Sofa Server Name</string>
    <string name="serverName_description">Set a default Server Name for your Sofa instance.</string>
    <string name="allowOtherServers_title">Allow Other Servers</string>
    <string name="allowOtherServers_description">Allow the user to change the above server URL.</string>
    <string name="username_title">Default Username</string>
    <string name="username_description">Set the username or email address to use to authenticate against the Sofa Server.</string>
    <string name="timeout_title">Default Request Timeout</string>
    <string name="timeout_description">How long in milliseconds the mobile app should wait for the server to respond.</string>
    <string name="vendor_title">EMM Vendor or Company Name</string>
    <string name="vendor_description">Name of the EMM vendor or company deploying the app. Used in help text when prompting for passcodes so users are aware why the app is being protected.</string>
    <string name="inAppSessionAuth_title">In-App Session Auth</string>
    <string name="inAppSessionAuth_description">Instead of default flow from the mobile browser, enforce SSO with the WebView.</string>
    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>

    <string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
        volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
        dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
        litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse blandit eleifend
        diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet, imperdiet nisl a,
        ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus
        egestas, est a condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed
        neque. Morbi tellus erat, dapibus ut sem a, iaculis tincidunt dui. Interdum et malesuada
        fames ac ante ipsum primis in faucibus. Curabitur et eros porttitor, ultricies urna vitae,
        molestie nibh. Phasellus at commodo eros, non aliquet metus. Sed maximus nisl nec dolor
        bibendum, vel congue leo egestas.\n\n
        Sed interdum tortor nibh, in sagittis risus mollis quis. Curabitur mi odio, condimentum sit
        amet auctor at, mollis non turpis. Nullam pretium libero vestibulum, finibus orci vel,
        molestie quam. Fusce blandit tincidunt nulla, quis sollicitudin libero facilisis et. Integer
        interdum nunc ligula, et fermentum metus hendrerit id. Vestibulum lectus felis, dictum at
        lacinia sit amet, tristique id quam. Cras eu consequat dui. Suspendisse sodales nunc ligula,
        in lobortis sem porta sed. Integer id ultrices magna, in luctus elit. Sed a pellentesque
        est.\n\n
        Aenean nunc velit, lacinia sed dolor sed, ultrices viverra nulla. Etiam a venenatis nibh.
        Morbi laoreet, tortor sed facilisis varius, nibh orci rhoncus nulla, id elementum leo dui
        non lorem. Nam mollis ipsum quis auctor varius. Quisque elementum eu libero sed commodo. In
        eros nisl, imperdiet vel imperdiet et, scelerisque a mauris. Pellentesque varius ex nunc,
        quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non viverra
        ipsum. Nunc quis augue egestas, cursus lorem at, molestie sem. Morbi a consectetur ipsum, a
        placerat diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus
        convallis.\n\n
        Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et
        malesuada fames ac turpis egestas. In volutpat arcu ut felis sagittis, in finibus massa
        gravida. Pellentesque id tellus orci. Integer dictum, lorem sed efficitur ullamcorper,
        libero justo consectetur ipsum, in mollis nisl ex sed nisl. Donec maximus ullamcorper
        sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus
        libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus
        vestibulum. Fusce dictum libero quis erat maximus, vitae volutpat diam dignissim.
    </string>
</resources>
